# 🚀 GUÍA EAS UPDATE - TS MÓVIL

## 📱 ¿QUÉ ES EAS UPDATE?

EAS Update te permite **actualizar el código JavaScript** de tu app **sin hacer un nuevo build nativo**. Esto significa:

- ✅ **Correcciones rápidas** sin esperar App Store Review
- ✅ **<PERSON><PERSON> builds nativos** = menos versiones en TestFlight
- ✅ **Updates instantáneos** para usuarios
- ✅ **Rollback rápido** si algo sale mal

---

## 🔧 COMANDOS PRINCIPALES

### **1. ACTUALIZAR CÓDIGO (Más Común)**
```bash
# Para correcciones de bugs, nuevas funcionalidades JS
eas update --branch production --message "Fix login issue"
```

### **2. ACTUALIZAR DESARROLLO**
```bash
# Para testing interno
eas update --branch development --message "Nueva funcionalidad en testing"
```

### **3. VER UPDATES DISPONIBLES**
```bash
# Ver historial de updates
eas update:list --branch production
```

### **4. ROLLBACK (Si algo sale mal)**
```bash
# Volver a update anterior
eas update:rollback --branch production
```

---

## 📋 CUÁNDO USAR CADA MÉTODO

### **🔄 USA EAS UPDATE PARA:**
- ✅ Correcciones de bugs JavaScript
- ✅ Cambios de UI/UX
- ✅ Nuevas funcionalidades que no requieren permisos nativos
- ✅ Actualizaciones de texto/contenido
- ✅ Cambios en lógica de negocio

### **🏗️ USA BUILD NATIVO PARA:**
- ❌ Cambios en permisos (Info.plist)
- ❌ Nuevas dependencias nativas
- ❌ Cambios en configuración de push notifications
- ❌ Actualizaciones de versión mayor
- ❌ Cambios en bundle identifier

---

## 🎯 FLUJO DE TRABAJO RECOMENDADO

### **Para Desarrollo Diario:**
```bash
# 1. Hacer cambios en código
# 2. Probar localmente
# 3. Actualizar desarrollo
eas update --branch development --message "Descripción del cambio"

# 4. Probar en TestFlight development build
# 5. Si funciona, actualizar producción
eas update --branch production --message "Descripción del cambio"
```

### **Para Emergencias:**
```bash
# Hotfix rápido en producción
eas update --branch production --message "HOTFIX: Critical bug fix"
```

---

## 📊 MONITOREO Y DEBUGGING

### **Ver estado de updates:**
```bash
# Ver updates por branch
eas update:list --branch production --limit 10

# Ver detalles de un update específico
eas update:view [UPDATE_ID]
```

### **Logs de updates:**
```bash
# Ver logs de deployment
eas update:configure
```

---

## ⚠️ LIMITACIONES IMPORTANTES

### **NO PUEDES ACTUALIZAR:**
- Permisos nativos (cámara, ubicación, etc.)
- Configuración de App Store Connect
- Bundle identifier o app name
- Dependencias que requieren linking nativo

### **USUARIOS DEBEN:**
- Tener conexión a internet para recibir updates
- Reiniciar la app para aplicar updates (automático)

---

## 🔄 CONFIGURACIÓN ACTUAL

Tu app ya está configurada con:

```json
// app.json
"updates": {
  "enabled": true,
  "checkAutomatically": "ON_LOAD",
  "fallbackToCacheTimeout": 0,
  "url": "https://u.expo.dev/7b50f48a-66b5-452b-826a-8e6ec802320f"
}
```

```json
// eas.json
"update": {
  "development": { "channel": "development" },
  "preview": { "channel": "preview" },
  "production": { "channel": "production" }
}
```

---

## 🚨 COMANDOS DE EMERGENCIA

### **Si algo sale mal:**
```bash
# 1. Rollback inmediato
eas update:rollback --branch production

# 2. Ver qué updates están activos
eas update:list --branch production

# 3. Forzar update específico
eas update:republish --update-id [UPDATE_ID]
```

---

## 💡 TIPS IMPORTANTES

1. **Siempre prueba en development** antes de production
2. **Usa mensajes descriptivos** en los updates
3. **Mantén un log** de qué cambios haces
4. **Ten un plan de rollback** para cambios críticos
5. **Monitorea errores** después de cada update

---

## 📞 SOPORTE

Si tienes problemas:
1. Revisa logs: `eas update:list`
2. Verifica configuración: `eas update:configure`
3. Rollback si es necesario: `eas update:rollback`

**¡Con EAS Update puedes hacer actualizaciones rápidas sin esperar App Store Review!**
