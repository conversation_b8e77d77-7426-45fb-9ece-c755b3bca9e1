# Documentación Multi-Tenant - Symfony 7.2

## 1. Introducción

Esta documentación describe la implementación de un sistema multi-tenant en Symfony 7.2 que permite manejar diferentes instancias de la aplicación para distintos clientes (tenants). Cada tenant tiene su propia base de datos y configuración específica.

## 2. Stack Tecnológico

- **Backend:** Symfony 7.2
- **Versión PHP:** 8.2
- **Autenticación API:** JWT (LexikJWTAuthenticationBundle)
- **Frontend:** React Native (mobile)
- **ORM:** Doctrine
- **Cache:** Symfony Cache Component
- **Logs:** Monolog con rotación de archivos

## 3. Tenants Disponibles

Actualmente se manejan dos tenants principales:

- **Transformación Digital (`ts`)**
  ```
  DATABASE_URL_TS=mysql://root:root@mysql:3306/msc-app-ts
  ```

- **Sindicato Nacional (`SNT`)**
  ```
  DATABASE_URL_SNT=mysql://root:root@mysql:3306/msc-app-ctm
  ```

Estas URLs están configuradas en el archivo `.env`:

## 4. Implementación Técnica

### 4.1 Configuración de Bases de Datos

```yaml
# config/packages/doctrine.yaml
doctrine:
    dbal:
        default_connection: default
        connections:
            default:
                url: '%env(resolve:DATABASE_URL)%'
            ts:
                url: '%env(resolve:DATABASE_URL_TS)%'
            SNT:
                url: '%env(resolve:DATABASE_URL_SNT)%'
```

### 4.2 TenantManager Service

El servicio `TenantManager` centraliza la lógica de gestión de tenants:

```php
class TenantManager
{
    private const ALLOWED_TENANTS = ['ts', 'SNT'];
    private $currentTenant = null;

    public function __construct(
        private EntityManagerInterface $em,
        private RequestStack $requestStack
    ) {}

    public function setCurrentTenant(string $tenant): void
    {
        if (!$this->isValidTenant($tenant)) {
            throw new NotFoundHttpException(sprintf('Tenant "%s" no es válido.', $tenant));
        }
        $this->currentTenant = $tenant;
        $this->switchDatabase($tenant);
    }

    public function getCurrentTenant(): ?string
    {
        if ($this->currentTenant) {
            return $this->currentTenant;
        }

        $request = $this->requestStack->getCurrentRequest();
        if (!$request) {
            throw new \RuntimeException('No request found');
        }

        $tenant = $request->attributes->get('dominio');
        if (!$this->isValidTenant($tenant)) {
            throw new \RuntimeException('Invalid tenant');
        }

        $this->currentTenant = $tenant;
        return $tenant;
    }

    public function switchDatabase(string $tenant): void
    {
        if (!$this->isValidTenant($tenant)) {
            throw new \RuntimeException('Invalid tenant');
        }

        // Configurar la conexión para el tenant actual
        $connection = $this->em->getConnection();
        $params = $connection->getParams();
        $params['dbname'] = $this->getTenantDatabaseName($tenant);
        $connection->close();
        $connection->__construct(
            $params,
            $connection->getDriver(),
            $connection->getConfiguration(),
            $connection->getEventManager()
        );
    }

    public function isValidTenant(string $tenant): bool
    {
        return in_array($tenant, self::ALLOWED_TENANTS);
    }

    private function getTenantDatabaseName(string $tenant): string
    {
        $databaseMapping = [
            'ts' => 'msc-app-ts',
            'SNT' => 'msc-app-ctm'
        ];

        if (!isset($databaseMapping[$tenant])) {
            throw new NotFoundHttpException(sprintf('No hay base de datos configurada para el tenant "%s".', $tenant));
        }

        return $databaseMapping[$tenant];
    }
}
```

### 4.3 Servicios Multi-tenant

#### Cache Service
```php
class TenantCacheService
{
    public function __construct(
        private TenantManager $tenantManager,
        private LoggerInterface $logger,
        private string $cacheDir
    ) {}

    public function get(string $key, callable $callback, int $expiresAfter = 3600)
    {
        $tenant = $this->tenantManager->getCurrentTenant();
        $cacheKey = sprintf('%s_%s', $tenant, $key);
        // Implementación de caché
    }
}
```

#### Logger Service
```php
class TenantLoggerService implements LoggerInterface
{
    public function __construct(
        private TenantManager $tenantManager,
        private string $logDir
    ) {}

    private function getLogger(): Logger
    {
        $tenant = $this->tenantManager->getCurrentTenant();
        // Implementación de logger específico por tenant
    }
}
```

#### Rate Limiter
```php
class TenantRateLimiter extends AbstractRequestRateLimiter
{
    protected function getLimiters(Request $request): array
    {
        $tenant = $this->tenantManager->getCurrentTenant();
        $user = $this->security->getUser();

        return [
            $this->factory->create($tenant.'_'.$request->getClientIp()),
            $this->factory->create($tenant.'_'.$userId)
        ];
    }
}
```

## 5. API Documentation

### 5.1 Autenticación

La autenticación se realiza mediante JWT (JSON Web Tokens). Cada token incluye información específica del tenant.

```
POST /{tenant}/api/login
```

**Parámetros:**
- `email`: Email del usuario
- `password`: Contraseña del usuario

**Respuesta:**
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
}
```

### 5.2 Rate Limiting

Cada tenant tiene sus propios límites:
- 100 solicitudes por IP por hora
- 100 solicitudes por usuario por hora

### 5.3 Endpoints Principales

Todos los endpoints requieren:
- Tenant en la URL: `/{tenant}/api/...`
- Token JWT: `Authorization: Bearer {token}`

#### Beneficios
```
GET    /{tenant}/api/benefits
POST   /{tenant}/api/benefits
PUT    /{tenant}/api/benefits/{id}
DELETE /{tenant}/api/benefits/{id}
```

#### Beneficiarios
```
GET    /{tenant}/api/beneficiaries
POST   /{tenant}/api/beneficiaries
PUT    /{tenant}/api/beneficiaries/{id}
DELETE /{tenant}/api/beneficiaries/{id}
```

#### Eventos
```
GET    /{tenant}/api/events
POST   /{tenant}/api/events
PUT    /{tenant}/api/events/{id}
DELETE /{tenant}/api/events/{id}
```

### 5.4 Manejo de Errores

```json
{
    "code": "ERROR_CODE",
    "message": "Descripción del error",
    "details": {}
}
```

**Códigos de Error:**
- `INVALID_TENANT`: Tenant no válido
- `TENANT_MISMATCH`: Tenant no coincide
- `RATE_LIMIT_EXCEEDED`: Límite excedido
- `INVALID_TOKEN`: Token inválido

## 6. Optimizaciones y Seguridad

### 6.1 Caché
- Caché independiente por tenant
- Tiempos de caché configurados:
  - Beneficios: 1 hora
  - Beneficiarios: 30 minutos
  - Eventos: 15 minutos

### 6.2 Logs
- Archivos separados por tenant
- Rotación automática de logs
- Formato: `/var/log/{tenant}.log`

### 6.3 Seguridad
- Bases de datos aisladas por tenant
- Validación de tenant en tokens JWT
- Rate limiting por IP y usuario
- Auditoría de accesos
- Validación estricta de tenant

## 7. Testing

### 7.1 Configuración de PHPUnit
PHPUnit está configurado en el proyecto a través de Composer y Docker. Para ejecutar las pruebas, se puede utilizar el contenedor Docker:

```bash
# Construir la imagen Docker con PHPUnit instalado
docker-compose build webappts

# Ejecutar las pruebas dentro del contenedor
docker-compose exec webappts bin/phpunit
```

### 7.2 Pruebas Unitarias
```bash
# Ejecutar pruebas específicas
docker-compose exec webappts bin/phpunit tests/Service/TenantManagerTest.php
```

### 7.3 Pruebas de Integración
```bash
# Ejecutar todas las pruebas de controladores API
docker-compose exec webappts bin/phpunit tests/Controller/Api

# Ejecutar una prueba específica
docker-compose exec webappts bin/phpunit tests/Controller/Api/AuthControllerTest.php
```

## 8. Mantenimiento y Monitoreo

### 8.1 Monitoreo
- Logs separados por tenant
- Métricas de rate limiting
- Auditoría de accesos

### 8.2 Backups
- Respaldos independientes por tenant
- Rotación de logs por tenant
- Limpieza periódica de caché 
