{"expo": {"name": "TS Móvil", "slug": "ts-movil", "version": "1.3.0", "orientation": "portrait", "icon": "./assets/images/favicon_TS.png", "userInterfaceStyle": "light", "newArchEnabled": false, "updates": {"enabled": true, "checkAutomatically": "ON_LOAD", "fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/7b50f48a-66b5-452b-826a-8e6ec802320f"}, "runtimeVersion": "1.3.0", "splash": {"image": "./assets/images/favicon_TS.png", "resizeMode": "contain", "backgroundColor": "#385927"}, "scheme": "ts", "ios": {"bundleIdentifier": "com.ts.msc", "buildNumber": "2", "supportsTablet": true, "infoPlist": {"NSFaceIDUsageDescription": "Permite que ts-movil use Face ID para autenticación."}}, "android": {"package": "com.ts.msc", "versionCode": 16, "adaptiveIcon": {"foregroundImage": "./assets/images/favicon_TS.png", "backgroundColor": "#385927"}, "googleServicesFile": "./google-services.json"}, "web": {"favicon": "./assets/images/favicon__TS.png"}, "extra": {"eas": {"projectId": "7b50f48a-66b5-452b-826a-8e6ec802320f"}}, "plugins": ["expo-secure-store", ["expo-local-authentication", {"faceIDPermission": "Allow ts-movil to use Face ID."}]]}}