# ===================================
# ANDROID SPECIFIC GITIGNORE
# ===================================

# Built application files
*.apk
*.ap_
*.aab

# Files for the ART/Dalvik VM
*.dex

# Java class files
*.class

# Generated files
bin/
gen/
out/
release/

# Gradle files
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Log Files
*.log

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# IntelliJ
*.iml
.idea/
.cxx/

# Keystore files (IMPORTANT: Never commit these!)
*.jks
*.keystore
key.properties

# Google Services (Firebase config)
google-services.json

# NDK
obj/

# Bundle artifacts
*.jsbundle

# Freeline
freeline.py
freeline/
freeline_project_description.json

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output
fastlane/readme.md

# lint
lint/intermediates/
lint/generated/
lint/outputs/
lint/tmp/
lint-results*.xml

# Android Profiling
*.hprof

# OSX
.DS_Store

# Windows
Thumbs.db
