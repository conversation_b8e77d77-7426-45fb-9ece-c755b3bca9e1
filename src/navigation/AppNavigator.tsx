// ✅ src/navigation/AppNavigator.tsx
import React, {useState, useCallback} from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {StatusBar} from 'react-native';
import {useAuth} from '../context/AuthContext';

import SplashScreen from '../screens/SplashScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import SuccessScreen from '../screens/auth/SuccessScreen';
import BottomTabs from './BottomTabs';
import AvisoPrivacidadScreen from '../screens/auth/AvisoPrivacidadScreen';
import ChangePasswordScreen from '../screens/auth/ChangePasswordScreen';
import BenefitDetailsScreen from '../screens/user/BenefitDetailsScreen';
import BeneficiariosScreen from "../screens/user/BeneficiariosScreen";
import NuevoBeneficiarioScreen from "../screens/user/NewBeneficiaryScreen";
import CredencialScreen from "../screens/user/CredencialScreen";
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';
import NewPasswordScreen from '../screens/auth/NewPasswordScreen';
import ValidateCodeScreen from '../screens/auth/ValidateCodeScreen';
import ValidateNumberScreen from "../screens/auth/ValidateNumberScreen";
import ConfirmPhoneScreen from "../screens/auth/ConfirmPhoneScreen";
import ErrorLogsScreen from "../screens/admin/ErrorLogsScreen";
import ChatScreen from "../screens/user/ChatScreen";
import {globalTheme} from "../constants/theme";


interface Benefit {
    id: string;
    title: string;
    description: string;
    validity_start_date: string;
    validity_end_date: string;
    image: string;
}

export type RootStackParamList = {
    BenefitDetails: {descuento: Benefit};
    AvisoPrivacidadScreen: {aceptable: boolean, previousScreen?: string, aceptado: boolean, form: any};
    Register: {aceptado: boolean, form: any}
    Login: undefined;
    ForgotPassword: undefined;
    NewPassword: {userId: number};
    ValidateCode: {method: string, userId: number, email?: string, phone?: string, isPasswordReset?: boolean};
    ConfirmPhone: {registeredPhone?: string, isPasswordReset?: boolean};
    ValidateNumber: {userId: string, phoneNumber?: string};
    Success: undefined;
    Main: undefined;
    BeneficiariosScreen: undefined;
    ChangePassword: undefined;
    NuevoBeneficiario: undefined;
    CredencialScreen: undefined;
    ErrorLogs: {dominio: string};
    ChatScreen: undefined;
}

const Stack = createNativeStackNavigator();

export default function AppNavigator() {
    const [isSplashFinished, setIsSplashFinished] = useState(false);
    const {authState} = useAuth();

    // Callback memoizado para evitar re-renders innecesarios
    const handleSplashFinish = useCallback(() => {
        setIsSplashFinished(true);
    }, []);

    if (!isSplashFinished || authState.loading) {
        return <SplashScreen onFinish={handleSplashFinish}/>;
    }

    return (
        <>
            <StatusBar barStyle="light-content" translucent={true} backgroundColor={globalTheme.gradient[0]}/>
            <NavigationContainer>
                <Stack.Navigator
                    id={undefined}
                    screenOptions={{
                        headerShown: false,
                        gestureEnabled: true,
                        animation: 'slide_from_right',
                        // Configuración más estable para iOS
                        freezeOnBlur: false,
                        // Transiciones más simples
                        animationTypeForReplace: 'pop',
                        presentation: 'card',
                    }}
                >
                    {!authState?.authenticated ? (
                        <>
                            <Stack.Screen name="Login" component={LoginScreen}/>
                            <Stack.Screen name="Register" component={RegisterScreen}/>
                            <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} options={{headerShown: false}}/>
                            <Stack.Screen name="NewPassword" component={NewPasswordScreen} options={{headerShown: false}}
                            />
                            <Stack.Screen name="ValidateCode" component={ValidateCodeScreen}/>
                            <Stack.Screen name="ConfirmPhone" component={ConfirmPhoneScreen}/>
                            <Stack.Screen name="ValidateNumber" component={ValidateNumberScreen}/>
                            <Stack.Screen name="Success" component={SuccessScreen}/>
                            <Stack.Screen name="AvisoPrivacidad" component={AvisoPrivacidadScreen}/>
                        </>
                    ) : (
                        <>
                            <Stack.Screen name="Main" component={BottomTabs}/>
                            <Stack.Screen name="BenefitDetails" component={BenefitDetailsScreen}/>
                            <Stack.Screen name="BeneficiariosScreen" component={BeneficiariosScreen}/>
                            <Stack.Screen name="ChangePassword" component={ChangePasswordScreen}/>
                            <Stack.Screen name="NuevoBeneficiario" component={NuevoBeneficiarioScreen}/>
                            <Stack.Screen name="CredencialScreen" component={CredencialScreen}/>
                            <Stack.Screen name="ErrorLogs" component={ErrorLogsScreen}/>
                            <Stack.Screen name="ChatScreen" component={ChatScreen}/>

                        </>
                    )}
                </Stack.Navigator>
            </NavigationContainer>
        </>
    );
}
