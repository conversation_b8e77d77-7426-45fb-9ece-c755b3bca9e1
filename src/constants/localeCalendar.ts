// i18n-calendar.ts
import { LocaleConfig } from 'react-native-calendars';

LocaleConfig.locales['es'] = {
    monthNames: [
        'ENERO','FEBRERO','MARZ<PERSON>','ABRI<PERSON>','<PERSON>Y<PERSON>','<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','SEPTIEMBRE','OCTUBRE','NOVIEMBRE','DICIEMBRE'
    ],
    monthNamesShort: [
        'ene','feb','mar','abr','may','jun',
        'jul','ago','sep','oct','nov','dic'
    ],
    dayNames: [
        'domingo','lunes','martes','miércoles','jueves','viernes','sábado'
    ],
    dayNamesShort: ['Do','Lu','Ma','Mi','Ju','Vi','Sa'],
    monthLastDay: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],

};

LocaleConfig.defaultLocale = 'es';
