// components/beneficiarios/GenderSelectorModal.tsx

import React from 'react';
import { Modal, View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import {vs} from "react-native-size-matters";
import {borderRadius, globalTheme} from "../../constants/theme";

interface Props {
    visible: boolean;
    onSelect: (gender: string) => void;
    onClose: () => void;
}

export default function GenderSelectorModal({ visible, onSelect, onClose }: Props) {
    return (
        <Modal
            animationType="fade"
            transparent
            visible={visible}
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <View style={styles.modalContent}>
                    <Text style={styles.title}>Selecciona el género</Text>

                    <TouchableOpacity style={styles.option} onPress={() => onSelect('MASCULINO')}>
                        <Text style={styles.optionText}>MASCULINO</Text>
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.option} onPress={() => onSelect('FEMENINO')}>
                        <Text style={styles.optionText}>FEMENINO</Text>
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.cancel} onPress={onClose}>
                        <Text style={styles.cancelText}>Cancelar</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.3)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: borderRadius,
        padding: vs(20),
        paddingVertical: vs(25),
        width: '85%',
        alignItems: 'center',
    },
    title: {
        fontSize: vs(16),
        fontWeight: 'bold',
        marginBottom: vs(10),
    },
    option: {
        paddingVertical: vs(10),
        width: '100%',
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    optionText: {
        fontSize: vs(12),
        fontWeight: '500'
    },
    cancel: {
        marginTop: vs(15),
        backgroundColor: globalTheme.gradient[1],
        paddingVertical: vs(6),
        width: '100%',
        borderRadius: vs(25)
    },
    cancelText: {
        color: '#fff',
        fontWeight: 'bold',
        textAlign: "center",
        fontSize: vs(14),
    },
});
