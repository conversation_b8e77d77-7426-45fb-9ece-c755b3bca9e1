// components/beneficiarios/EducationSelectorModal.tsx

import React from 'react';
import { Modal, View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import {vs} from "react-native-size-matters";
import {borderRadius, globalTheme} from "../../constants/theme";

interface Props {
    visible: boolean;
    onSelect: (education: string) => void;
    onClose: () => void;
}

const educationOptions = [
    'SIN ESTUDIOS',
    'PREESCOLAR',
    'PRIMARIA INCOMPLETA',
    'PRIMARIA COMPLETA',
    'SECUNDARIA INCOMPLETA',
    'SECUNDARIA COMPLETA',
    'PREPARATORIA INCOMPLETA',
    'PREPARATORIA COMPLETA',
    'TÉCNICO',
    'LICENCIATURA INCOMPLETA',
    'LICENCIATURA COMPLETA',
    'MAESTRÍA',
    'DOCTORADO',
    'OTRO'
];

export default function EducationSelectorModal({ visible, onSelect, onClose }: Props) {
    return (
        <Modal
            animationType="fade"
            transparent
            visible={visible}
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <View style={styles.modalContent}>
                    <Text style={styles.title}>Selecciona el nivel educativo</Text>
                    
                    <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
                        {educationOptions.map((option, index) => (
                            <TouchableOpacity 
                                key={index} 
                                style={styles.option} 
                                onPress={() => onSelect(option)}
                            >
                                <Text style={styles.optionText}>{option}</Text>
                            </TouchableOpacity>
                        ))}
                    </ScrollView>

                    <TouchableOpacity style={styles.cancel} onPress={onClose}>
                        <Text style={styles.cancelText}>Cancelar</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.3)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: borderRadius,
        padding: vs(20),
        paddingVertical: vs(25),
        width: '85%',
        maxHeight: '70%',
        alignItems: 'center',
    },
    title: {
        fontSize: vs(16),
        fontWeight: 'bold',
        marginBottom: vs(15),
        color: globalTheme.text_contrast,
    },
    scrollContainer: {
        width: '100%',
        maxHeight: vs(300),
    },
    option: {
        paddingVertical: vs(12),
        width: '100%',
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    optionText: {
        fontSize: vs(12),
        fontWeight: '500',
        color: globalTheme.text_contrast,
    },
    cancel: {
        marginTop: vs(15),
        backgroundColor: globalTheme.gradient[1],
        paddingVertical: vs(8),
        width: '100%',
        borderRadius: vs(25)
    },
    cancelText: {
        color: '#fff',
        fontWeight: 'bold',
        textAlign: "center",
        fontSize: vs(14),
    },
});
