// components/beneficiarios/BeneficiarioCard.tsx

import React from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import {formatFecha} from '../../utils/dateUtils';
import {vs} from "react-native-size-matters";
import {Ionicons} from "@expo/vector-icons";
import {borderRadius, globalTheme} from "../../constants/theme";

interface Props {
    nombre: string,
    kinship: string,
    fechaNacimiento: string,
    foto?: string,
    onEdit: () => void,
    setDeleteModal: (modal: boolean) => void,
    setBeneficiarySelected:  (beneficiary: number) => void,
    beneficiarioId: number,
    relacion?: string
}

const size = 70
const photoWidth = vs(size)
const photoRadius = vs(size)

export default function BeneficiarioCard({
                                             nombre,
                                             kinship,
                                             fechaNacimiento,
                                             foto,
                                             onEdit,
                                             setDeleteModal,
                                             setBeneficiarySelected,
                                             beneficiarioId,
                                             relacion
                                         }: Props) {
    console.log('👥 BeneficiarioCard - Renderizando:', {
        nombre,
        foto: foto ? foto.substring(0, 50) + '...' : 'NO FOTO',
        hasFoto: !!foto
    });

    return (
        <View style={styles.card}>
            <Image
                source={foto ? {uri: foto} : require('../../../assets/images/avatar-default.png')}
                style={styles.photo}
                onLoad={() => console.log('✅ Foto beneficiario cargada:', nombre)}
                onError={(error) => console.error('❌ Error al cargar foto beneficiario:', nombre, error.nativeEvent.error)}
                onLoadStart={() => console.log('⏳ Iniciando carga foto beneficiario:', nombre)}
                onLoadEnd={() => console.log('🏁 Finalizó carga foto beneficiario:', nombre)}
            />
            <View style={styles.info}>
                <Text style={styles.name}>{nombre}</Text>
                <Text style={styles.relation}>{kinship ? kinship.toUpperCase() : 'SIN RELACIÓN'}</Text>

                <Text style={styles.birth}>{formatFecha(fechaNacimiento)}</Text>

                <View style={styles.actions}>
                    <TouchableOpacity onPress={onEdit} style={styles.editBtn}>
                        <Text style={styles.btnText}>EDITAR</Text>
                        <Ionicons name={'pencil'} color={"#FFF"} size={vs(10)}/>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {
                        setDeleteModal(true)
                        setBeneficiarySelected(beneficiarioId)
                    }} style={styles.deleteBtn}>
                        <Text style={styles.btnText}>ELIMINAR</Text>
                        <Ionicons name={'close'} color={"#FFF"} size={vs(10)}/>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}


const styles = StyleSheet.create({
    card: {
        flexDirection: 'row',
        backgroundColor: globalTheme.container_translucent,
        borderRadius: borderRadius,
        padding: vs(10),
        marginBottom: vs(15),
        alignItems: 'center',
    },
    photo: {
        width: photoWidth,
        height: photoWidth,
        borderRadius: photoRadius,
    },
    info: {
        flex: 1,
        marginLeft: vs(5)
    },
    name: {
        fontSize: vs(14),
        marginBottom: 2,
        color: globalTheme.text_head
    },
    relation: {
        fontSize: vs(14),
        color: globalTheme.text_head,
        marginBottom: 2,
        fontWeight: "bold"
    },
    birth: {
        fontSize: vs(11),
        color: globalTheme.text_head,
        marginBottom: 8,
    },
    actions: {
        flexDirection: 'row',
        gap: 5,
    },
    editBtn: {
        backgroundColor: globalTheme.button_ok,
        paddingHorizontal: vs(6),
        paddingVertical: vs(2),
        borderRadius: borderRadius,
        flexDirection: "row",
        alignItems: "center",
        gap: 2
    },
    deleteBtn: {
        backgroundColor: globalTheme.button_bad,
        paddingHorizontal: vs(6),
        paddingVertical: vs(2),
        borderRadius: borderRadius,
        flexDirection: "row",
        alignItems: "center",
        gap: 2
    },
    btnText: {
        color: globalTheme.text_head,
        fontSize: vs(11),
    },
});
