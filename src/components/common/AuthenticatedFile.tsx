import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Linking, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../context/AuthContext';
import { globalTheme } from '../../constants/theme';
import AuthenticatedImage from './AuthenticatedImage';
import { getFileExtension, isImageFile, getFileIcon, getMimeTypeFromExtension } from '../../services/fileService';

interface AuthenticatedFileProps {
  uri: string;
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  style?: any;
  onPress?: () => void;
  showPreview?: boolean;
}

export default function AuthenticatedFile({
  uri,
  fileName,
  fileSize,
  mimeType,
  style,
  onPress,
  showPreview = true,
}: AuthenticatedFileProps) {
  const { authState } = useAuth();
  const [fileInfo, setFileInfo] = useState<{
    name: string;
    size: string;
    type: string;
    isImage: boolean;
  } | null>(null);

  useEffect(() => {
    if (!uri) return;

    // Determinar información del archivo usando el servicio
    const name = fileName || uri.split('/').pop() || 'Archivo';
    const extension = getFileExtension(name);
    const isImage = mimeType?.startsWith('image/') || isImageFile(name);
    const size = fileSize ? formatFileSize(fileSize) : 'Tamaño desconocido';
    const type = mimeType || getMimeTypeFromExtension(extension);

    setFileInfo({
      name,
      size,
      type,
      isImage,
    });
  }, [uri, fileName, fileSize, mimeType]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileTypeFromExtension = (filename: string): string => {
    const ext = getFileExtension(filename);
    switch (ext) {
      case 'pdf': return 'PDF';
      case 'doc':
      case 'docx': return 'Word';
      case 'xls':
      case 'xlsx': return 'Excel';
      case 'ppt':
      case 'pptx': return 'PowerPoint';
      case 'txt': return 'Texto';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif': return 'Imagen';
      default: return 'Archivo';
    }
  };

  const handleFilePress = async () => {
    if (onPress) {
      onPress();
      return;
    }

    // Para archivos que requieren autenticación, descargar y abrir
    if (uri.includes('/ts/api/') && authState.token) {
      try {
        const response = await fetch(uri, {
          headers: {
            'Authorization': `Bearer ${authState.token}`,
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // En React Native, no podemos descargar archivos directamente
        // Mostrar mensaje informativo
        Alert.alert(
          'Archivo disponible',
          `El archivo "${fileInfo?.name}" está disponible en el servidor.`,
          [
            { text: 'OK', style: 'default' }
          ]
        );
      } catch (error: any) {
        console.error('Error accessing file:', error);
        Alert.alert(
          'Error',
          'No se pudo acceder al archivo. Verifica tu conexión e intenta nuevamente.',
          [{ text: 'OK', style: 'default' }]
        );
      }
    } else {
      // Para URLs públicas, intentar abrir con el navegador
      try {
        await Linking.openURL(uri);
      } catch (error) {
        console.error('Error opening file:', error);
        Alert.alert(
          'Error',
          'No se pudo abrir el archivo.',
          [{ text: 'OK', style: 'default' }]
        );
      }
    }
  };

  if (!fileInfo) {
    return (
      <View style={[styles.container, style]}>
        <Text style={styles.loadingText}>Cargando información del archivo...</Text>
      </View>
    );
  }

  // Si es una imagen y se debe mostrar preview, usar AuthenticatedImage
  if (fileInfo.isImage && showPreview) {
    return (
      <View style={[styles.imageContainer, style]}>
        <AuthenticatedImage
          uri={uri}
          style={styles.imagePreview}
          resizeMode="cover"
        />
        <View style={styles.fileInfo}>
          <Text style={styles.fileName} numberOfLines={1}>
            {fileInfo.name}
          </Text>
          <Text style={styles.fileDetails}>
            {fileInfo.size} • {fileInfo.type}
          </Text>
        </View>
      </View>
    );
  }

  // Para otros archivos o cuando no se muestra preview
  return (
    <TouchableOpacity
      style={[styles.fileContainer, style]}
      onPress={handleFilePress}
      activeOpacity={0.7}
    >
      <View style={styles.fileIcon}>
        <Ionicons
          name={getFileIcon(fileInfo.name) as any}
          size={32}
          color={globalTheme.gradient[1]}
        />
      </View>
      <View style={styles.fileInfo}>
        <Text style={styles.fileName} numberOfLines={1}>
          {fileInfo.name}
        </Text>
        <Text style={styles.fileDetails}>
          {fileInfo.size} • {fileInfo.type}
        </Text>
      </View>
      <Ionicons
        name="chevron-forward"
        size={20}
        color="#999"
      />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
  },
  imageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  imagePreview: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  fileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  fileIcon: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: globalTheme.gradient[1] + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  fileDetails: {
    fontSize: 12,
    color: '#666',
  },
});
