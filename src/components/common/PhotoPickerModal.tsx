// src/components/common/PhotoPickerModal.tsx

import React, { useState } from 'react';
import { Modal, View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { getPhotoConfirmationConfig } from '../../utils/alertUtils';
import ConfirmationModal from './ConfirmationModal';
import {vs} from "react-native-size-matters";
import {globalTheme} from "../../constants/theme";

interface Props {
    visible: boolean;
    onClose: () => void;
    onPick: (uri: string) => void;
    isForBeneficiary?: boolean; // Nueva prop para distinguir el contexto
}

export default function PhotoPickerModal({ visible, onClose, onPick, isForBeneficiary = false }: Props) {
    const [showConfirmation, setShowConfirmation] = useState(false);
    const [selectedPhotoUri, setSelectedPhotoUri] = useState<string | null>(null);

    const handleConfirmPhoto = () => {
        if (selectedPhotoUri) {
            console.log('✅ Usuario confirmó actualización de foto');
            onPick(selectedPhotoUri);
            setShowConfirmation(false);
            setSelectedPhotoUri(null);
            onClose();
        }
    };

    const handleCancelPhoto = () => {
        console.log('❌ Usuario canceló actualización de foto');
        setShowConfirmation(false);
        setSelectedPhotoUri(null);
    };
    const handleCamera = async () => {
        console.log('📷 Iniciando cámara...');
        try {
            console.log('🔐 Solicitando permisos de cámara...');
            const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
            console.log('✅ Resultado de permisos:', permissionResult);

            if (!permissionResult.granted) {
                console.log('❌ Permiso de cámara denegado');
                alert('Permiso de cámara denegado');
                return;
            }

            console.log('📷 Abriendo cámara...');
            const result = await ImagePicker.launchCameraAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                quality: 0.7,
            });
            console.log('📸 Resultado de cámara:', result);

            if (!result.canceled && result.assets && result.assets.length > 0) {
                const photoUri = result.assets[0].uri;
                console.log('✅ Foto capturada:', photoUri);
                setSelectedPhotoUri(photoUri);
                setShowConfirmation(true);
            } else {
                console.log('❌ Captura de foto cancelada o falló');
                onClose();
            }
        } catch (error) {
            console.error('❌ Error al abrir la cámara:', error);
            alert('Error al abrir la cámara. Intenta de nuevo.');
            onClose();
        }
    };

    const handleGallery = async () => {
        console.log('🖼️ Iniciando galería...');
        try {
            console.log('🔐 Solicitando permisos de galería...');
            const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
            console.log('✅ Resultado de permisos:', permissionResult);

            if (!permissionResult.granted) {
                console.log('❌ Permiso de galería denegado');
                alert('Permiso de galería denegado');
                return;
            }

            console.log('🖼️ Abriendo galería...');
            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                quality: 0.7,
            });
            console.log('📸 Resultado de galería:', result);

            if (!result.canceled && result.assets && result.assets.length > 0) {
                const photoUri = result.assets[0].uri;
                console.log('✅ Foto seleccionada:', photoUri);
                setSelectedPhotoUri(photoUri);
                setShowConfirmation(true);
            } else {
                console.log('❌ Selección de foto cancelada o falló');
                onClose();
            }
        } catch (error) {
            console.error('❌ Error al abrir la galería:', error);
            alert('Error al abrir la galería. Intenta de nuevo.');
            onClose();
        }
    };

    return (
        <Modal visible={visible} transparent animationType="fade">
            <View style={styles.overlay}>
                <View style={styles.modal}>
                    <Text style={styles.title}>Selecciona una opción</Text>

                    <TouchableOpacity style={styles.button} onPress={handleCamera}>
                        <Text style={styles.buttonText}>Tomar Foto</Text>
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.button} onPress={handleGallery}>
                        <Text style={styles.buttonText}>Elegir de Galería</Text>
                    </TouchableOpacity>

                    <TouchableOpacity style={[styles.button, styles.cancelButton]} onPress={onClose}>
                        <Text style={styles.cancelButtonText}>Cancelar</Text>
                    </TouchableOpacity>
                </View>
            </View>

            {/* Modal de Confirmación Bonito */}
            <ConfirmationModal
                visible={showConfirmation}
                {...getPhotoConfirmationConfig(isForBeneficiary)}
                onConfirm={handleConfirmPhoto}
                onCancel={handleCancelPhoto}
            />
        </Modal>
    );
}

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.3)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modal: {
        width: '85%',
        backgroundColor: 'white',
        borderRadius: vs(25),
        paddingVertical: vs(20),
        paddingHorizontal: vs(27),
        alignItems: 'center',
    },
    title: {
        fontSize: vs(18),
        fontWeight: 'bold',
        marginBottom: vs(25),
    },
    button: {
        backgroundColor:  globalTheme.gradient[1],
        borderRadius: vs(25),
        paddingVertical: vs(8),
        marginBottom: vs(10),
        width: '100%',
        alignItems: 'center',
    },
    buttonText: {
        color: 'white',
        fontSize: vs(14),
        fontWeight: "600"
    },
    cancelButton: {
        backgroundColor: '#8797B8',
    },
    cancelButtonText: {
        color: '#ffffff',
        fontSize: vs(14),
        fontWeight: "600"
    },
});
