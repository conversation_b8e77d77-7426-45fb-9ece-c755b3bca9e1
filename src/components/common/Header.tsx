import React, {useState} from 'react';
import {View, TouchableOpacity, StyleSheet, Modal, Text, ActivityIndicator, Dimensions} from 'react-native';
import { BlurView } from 'expo-blur';
import {Ionicons} from '@expo/vector-icons';
import {mvs, vs} from 'react-native-size-matters';
import {useAuth} from '../../context/AuthContext';
import LogoTS from "../svg/LogoTS";
import {borderRadius, globalTheme} from "../../constants/theme";


const CustomLogoutModal = ({ visible, onClose, onLogout }) => {
    return (
        <Modal
            transparent={true}
            visible={visible}
            animationType="fade"
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <View style={styles.modalContainer}>
                    <Text style={styles.title}>Cerrar sesión</Text>
                    <Text style={styles.message}>¿Seguro de salir?</Text>

                    <View style={styles.buttonContainer}>
                        <TouchableOpacity style={[styles.button, styles.logoutButton]} onPress={onLogout}>
                            <Text style={[styles.buttonText, styles.logoutButtonText]}>Salir</Text>
                        </TouchableOpacity>

                        <TouchableOpacity style={[styles.button, styles.cancelButton]} onPress={onClose}>
                            <Text style={[styles.buttonText, {color: globalTheme.text_head}]}>Quedarse</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};


export default function Header() {
    const {logout} = useAuth();

    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(false);

    const handleLogout = () => {
        setModalVisible(true); // Mostrar el modal cuando se presiona "Cerrar sesión"
    };

    const handleConfirmLogout = async () => {
        setLoading(true);
        setModalVisible(false);
        try {
            await logout();
            // No necesitamos llamar onLogout ya que el AuthContext maneja la navegación automáticamente
        } catch (error) {
            console.error('❌ Error al cerrar sesión:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <View style={styles.container}>
            <LogoTS size={"sm"} />
            <TouchableOpacity style={styles.logoutBtn} onPress={handleLogout}>
                <Ionicons name="log-out-outline" size={mvs(35, 0.5)} color="#fff"/>
            </TouchableOpacity>

            {/* Modal de confirmación de cierre de sesión */}
            <CustomLogoutModal
                visible={modalVisible}
                onClose={() => setModalVisible(false)} // Cerrar modal
                onLogout={handleConfirmLogout}
            />

            {/* Loading Overlay */}
            {loading && (
                <BlurView intensity={20} tint="dark" style={loadingOverlayStyles.loadingOverlay}>
                    <View style={loadingOverlayStyles.loadingCard}>
                        <ActivityIndicator size="large" color="#1698BF" />
                        <Text style={loadingOverlayStyles.loadingText}>Cerrando sesión...</Text>
                    </View>
                </BlurView>
            )}

        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        width: '90%',
        height: vs(75),
        alignSelf: 'center',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    logoutBtn: {
        flexDirection: 'row',
    },
    overlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    modalContainer: {
        backgroundColor: globalTheme.background,
        paddingVertical: vs(20),
        borderRadius: borderRadius,
        width: '80%',
        maxWidth: 400,
        alignItems: 'center',
    },
    title: {
        fontSize: vs(20),
        color: globalTheme.text_contrast,
        fontWeight: 'bold',
        marginBottom: vs(6),
    },
    message: {
        fontSize: vs(14),
        textAlign: 'center',
        marginBottom: vs(20),
        fontWeight: "400"
    },
    buttonContainer: {
        flexDirection: 'row',
        width: '90%',
        justifyContent: 'space-evenly',
    },
    button: {
        paddingVertical: vs(6),
        paddingHorizontal: vs(10),
        borderRadius: borderRadius,
        width: '45%',
        justifyContent: "center"
    },
    cancelButton: {
        backgroundColor: globalTheme.container_dark,
    },
    logoutButton: {
        backgroundColor: globalTheme.container_light,
    },
    buttonText: {
        textAlign: 'center',
        color: globalTheme.text_contrast,
        fontSize: vs(14),
        fontWeight: "500"
    },
    logoutButtonText: {
        color: globalTheme.button_bad, // Rojo para el botón de salir
        fontWeight: "600"
    },
});

const { width, height } = Dimensions.get('window');

const loadingOverlayStyles = {
    loadingOverlay: {
        position: 'absolute' as const,
        top: -height, // Usar altura de pantalla completa
        left: -width, // Usar ancho de pantalla completa
        width: width * 3, // Asegurar cobertura completa
        height: height * 3, // Asegurar cobertura completa
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
        zIndex: 1000,
    },
    loadingCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 25,
        borderRadius: 15,
        alignItems: 'center' as const,
        minWidth: 140,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    loadingText: {
        marginTop: 12,
        color: '#000000',
        fontSize: 15,
        fontWeight: '600' as const,
        textAlign: 'center' as const,
    },
};
