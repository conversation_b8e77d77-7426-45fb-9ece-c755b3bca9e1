import React, { useState, useEffect } from 'react';
import { View, Image, ActivityIndicator, Text, StyleSheet } from 'react-native';
import { useAuth } from '../../context/AuthContext';
import { globalTheme } from '../../constants/theme';

interface AuthenticatedImageProps {
  uri: string;
  style?: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  onError?: (error: any) => void;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
}

export default function AuthenticatedImage({
  uri,
  style,
  resizeMode = 'contain',
  onError,
  onLoadStart,
  onLoadEnd,
}: AuthenticatedImageProps) {
  const { authState } = useAuth();
  const [imageData, setImageData] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!uri) {
      setLoading(false);
      return;
    }

    // Si es una URI local, mostrarla directamente
    if (uri.startsWith('file://')) {
      setImageData(uri);
      setLoading(false);
      return;
    }

    // Si no requiere autenticación, mostrarla directamente
    if (!uri.includes('/ts/api/')) {
      setImageData(uri);
      setLoading(false);
      return;
    }

    // Para URLs que requieren autenticación, descargar la imagen
    fetchAuthenticatedImage();
  }, [uri, authState.token]);

  const fetchAuthenticatedImage = async () => {
    if (!authState.token) {
      setError('No hay token de autenticación');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      onLoadStart?.();

      const response = await fetch(uri, {
        headers: {
          'Authorization': `Bearer ${authState.token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // En React Native, convertir la respuesta a base64
      const arrayBuffer = await response.arrayBuffer();
      const base64 = btoa(
        new Uint8Array(arrayBuffer)
          .reduce((data, byte) => data + String.fromCharCode(byte), '')
      );

      // Determinar el tipo MIME de la respuesta
      const contentType = response.headers.get('content-type') || 'image/jpeg';
      const dataUri = `data:${contentType};base64,${base64}`;

      setImageData(dataUri);
      setLoading(false);
      onLoadEnd?.();
    } catch (err: any) {
      console.error('Error loading authenticated image:', err);
      setError(err.message);
      setLoading(false);
      onError?.(err);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, style]}>
        <ActivityIndicator size="small" color={globalTheme.gradient[1]} />
        <Text style={styles.loadingText}>Cargando imagen...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, style]}>
        <Text style={styles.errorText}>Error al cargar imagen</Text>
        <Text style={styles.errorDetail}>{error}</Text>
      </View>
    );
  }

  if (!imageData) {
    return (
      <View style={[styles.container, style]}>
        <Text style={styles.errorText}>No hay imagen</Text>
      </View>
    );
  }

  return (
    <Image
      source={{ uri: imageData }}
      style={style}
      resizeMode={resizeMode}
      onError={onError}
      onLoadStart={onLoadStart}
      onLoadEnd={onLoadEnd}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 12,
    color: globalTheme.gradient[1],
  },
  errorText: {
    fontSize: 14,
    color: '#ff4444',
    textAlign: 'center',
    marginBottom: 4,
  },
  errorDetail: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});
