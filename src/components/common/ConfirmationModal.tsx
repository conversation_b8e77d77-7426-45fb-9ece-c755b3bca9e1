import React from 'react';
import {
    Modal,
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { vs, mvs } from 'react-native-size-matters';
import { globalTheme } from '../../constants/theme';

interface Props {
    visible: boolean;
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void;
    onCancel: () => void;
    icon?: string;
}

const { width, height } = Dimensions.get('window');

export default function ConfirmationModal({
    visible,
    title,
    message,
    confirmText = 'Sí',
    cancelText = 'No',
    onConfirm,
    onCancel,
    icon = '📸'
}: Props) {
    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="fade"
            statusBarTranslucent={true}
        >
            <BlurView intensity={20} tint="dark" style={styles.overlay}>
                <View style={styles.container}>
                    <View style={styles.modal}>
                        {/* Icono */}
                        <View style={styles.iconContainer}>
                            <Text style={styles.icon}>{icon}</Text>
                        </View>

                        {/* Título */}
                        <Text style={styles.title}>{title}</Text>

                        {/* Mensaje */}
                        <Text style={styles.message}>{message}</Text>

                        {/* Botones */}
                        <View style={styles.buttonContainer}>
                            <TouchableOpacity
                                style={[styles.button, styles.cancelButton]}
                                onPress={onCancel}
                                activeOpacity={0.8}
                            >
                                <Text style={styles.cancelButtonText}>{cancelText}</Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={[styles.button, styles.confirmButton]}
                                onPress={onConfirm}
                                activeOpacity={0.8}
                            >
                                <Text style={styles.confirmButtonText}>{confirmText}</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </BlurView>
        </Modal>
    );
}

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    container: {
        width: width * 0.85,
        maxWidth: 400,
    },
    modal: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderRadius: 20,
        padding: mvs(25),
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.3,
        shadowRadius: 15,
        elevation: 12,
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.3)',
    },
    iconContainer: {
        width: vs(60),
        height: vs(60),
        borderRadius: vs(30),
        backgroundColor: globalTheme.gradient[0],
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: mvs(15),
    },
    icon: {
        fontSize: vs(28),
    },
    title: {
        fontSize: vs(18),
        fontWeight: 'bold',
        color: globalTheme.container_dark,
        textAlign: 'center',
        marginBottom: mvs(10),
    },
    message: {
        fontSize: vs(14),
        color: globalTheme.container_medium,
        textAlign: 'center',
        lineHeight: vs(20),
        marginBottom: mvs(25),
    },
    buttonContainer: {
        flexDirection: 'row',
        width: '100%',
        gap: mvs(12),
    },
    button: {
        flex: 1,
        paddingVertical: vs(12),
        borderRadius: 15,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: vs(45),
    },
    cancelButton: {
        backgroundColor: 'rgba(108, 117, 125, 0.1)',
        borderWidth: 1,
        borderColor: 'rgba(108, 117, 125, 0.3)',
    },
    confirmButton: {
        backgroundColor: globalTheme.gradient[0],
        shadowColor: globalTheme.gradient[0],
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 6,
        elevation: 4,
    },
    cancelButtonText: {
        color: globalTheme.container_medium,
        fontSize: vs(14),
        fontWeight: '600',
    },
    confirmButtonText: {
        color: globalTheme.text_head,
        fontSize: vs(14),
        fontWeight: 'bold',
    },
});
