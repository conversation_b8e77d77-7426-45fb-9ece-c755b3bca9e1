// components/common/UserAvatar.tsx

import React from 'react';
import {Image, TouchableOpacity, ImageStyle, View} from 'react-native';
import {Ionicons} from "@expo/vector-icons";
import {vs} from "react-native-size-matters";
import {globalTheme} from "../../constants/theme";
import {normalizeImageUrl} from "../../utils/imageUtils";

type Props = {
    uri?: string | null;
    size?: number;
    style?: ImageStyle;
    onPress?: () => void;
    isEditing?: boolean;
};

export default function UserAvatar({uri, size = 100, style, onPress, isEditing}: Props) {
    // Normalizar la URI para corregir problemas de localhost
    const normalizedUri = uri ? normalizeImageUrl(uri) : uri;

    // Log solo si hay problemas con localhost
    if (uri && (uri.includes('localhost') || uri.includes('127.0.0.1'))) {
        console.error('🚨 UserAvatar - URL con localhost detectada:', uri);
        console.log('✅ UserAvatar - URL corregida:', normalizedUri);
    }

    console.log('👤 UserAvatar - Renderizando:', {
        uri: normalizedUri ? normalizedUri.substring(0, 50) + '...' : 'NO URI',
        hasUri: !!normalizedUri,
        size
    });

    const avatar = (
        <Image
            source={
                normalizedUri
                    ? {uri: normalizedUri}
                    : require('../../../assets/images/avatar-default.png') // Cambia por tu imagen default si quieres
            }
            style={[
                {
                    width: size,
                    height: size,
                    borderRadius: size / 2,
                    backgroundColor: '#ccc',
                },
                style,
            ]}
            onLoad={() => console.log('✅ Avatar cargado exitosamente')}
            onError={(error) => console.error('❌ Error al cargar avatar:', error.nativeEvent.error)}
            onLoadStart={() => console.log('⏳ Iniciando carga de avatar')}
            onLoadEnd={() => console.log('🏁 Finalizó carga de avatar')}
        />
    );

    if (onPress) {
        return (
            <View>
                <TouchableOpacity activeOpacity={0.8} onPress={onPress}>
                    {avatar}

                {isEditing && (
                    <Ionicons name={'camera'} style={{
                        color: '#fff',
                        fontSize: vs(20),
                        position: 'absolute',
                        alignSelf: 'center',
                        bottom: -vs(17),
                        backgroundColor: globalTheme.gradient[1],
                        borderRadius: vs(30),
                        padding: vs(5)
                    }}/>
                )}
                </TouchableOpacity>
            </View>
        );
    }

    return avatar;
}
