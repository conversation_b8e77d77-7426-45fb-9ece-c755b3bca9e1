import {Text, View} from "react-native";
import {Ionicons} from "@expo/vector-icons";
import {vs} from "react-native-size-matters";
import {globalTheme} from "../../constants/theme";

const ErrorComponent = () => {
    return (
        <View style={{ justifyContent: "space-evenly", alignItems: "center", height: vs(350), marginTop: vs(70)}}>
            <Ionicons
                name={"cloud-offline-outline"}
                color={globalTheme.gradient[0]}
                size={vs(150)}
            />
            <View>
                <Text> Oops! Algo salió mal. </Text>
            </View>
        </View>
    )
}

export default ErrorComponent;