import React from 'react';
import { Modal, View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { vs } from "react-native-size-matters";
import { globalTheme } from "../../constants/theme";

interface Props {
    visible: boolean;
    onClose: () => void;
    onPick: (uri: string) => void;
    type: 'photo' | 'file'; // Determina qué opciones mostrar
    title?: string;
}

export default function FilePickerModal({ 
    visible, 
    onClose, 
    onPick, 
    type,
    title 
}: Props) {
    
    const modalTitle = title || (type === 'photo' ? 'Selecciona una foto' : 'Selecciona una foto/imagen');

    const handleCamera = async () => {
        console.log('📷 Iniciando cámara...');
        try {
            const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
            
            if (!permissionResult.granted) {
                Alert.alert('Permiso requerido', 'Se necesita permiso para acceder a la cámara');
                return;
            }

            const result = await ImagePicker.launchCameraAsync({
                mediaTypes: ['images'],
                allowsEditing: true,
                quality: 0.7,
            });

            if (!result.canceled && result.assets && result.assets.length > 0) {
                const photoUri = result.assets[0].uri;
                console.log('✅ Foto capturada:', photoUri);
                onPick(photoUri);
                onClose();
            } else {
                onClose();
            }
        } catch (error) {
            console.error('❌ Error al abrir la cámara:', error);
            Alert.alert('Error', 'Error al abrir la cámara. Intenta de nuevo.');
            onClose();
        }
    };

    const handleGallery = async () => {
        console.log('🖼️ Iniciando galería...');
        try {
            const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
            
            if (!permissionResult.granted) {
                Alert.alert('Permiso requerido', 'Se necesita permiso para acceder a la galería');
                return;
            }

            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ['images'],
                allowsEditing: true,
                quality: 0.7,
            });

            if (!result.canceled && result.assets && result.assets.length > 0) {
                const photoUri = result.assets[0].uri;
                console.log('✅ Foto seleccionada:', photoUri);
                onPick(photoUri);
                onClose();
            } else {
                onClose();
            }
        } catch (error) {
            console.error('❌ Error al abrir la galería:', error);
            Alert.alert('Error', 'Error al abrir la galería. Intenta de nuevo.');
            onClose();
        }
    };

    const handleDocuments = async () => {
        console.log('📄 Iniciando selector de documentos...');

        try {
            const result = await DocumentPicker.getDocumentAsync({
                type: '*/*', // Permitir todos los tipos de archivos
                copyToCacheDirectory: true, // Copiar a directorio accesible
                multiple: false,
            });

            console.log('📄 Resultado del selector:', result);

            if (!result.canceled && result.assets && result.assets.length > 0) {
                const file = result.assets[0];
                const fileUri = file.uri;
                const fileName = file.name;

                console.log('✅ Archivo seleccionado:', {
                    name: fileName,
                    uri: fileUri,
                    type: file.mimeType,
                    size: file.size
                });

                // Enviar la URI del archivo
                onPick(fileUri);
                onClose();
            } else {
                console.log('❌ No se seleccionó archivo o se canceló');
                onClose();
            }
        } catch (error: any) {
            console.error('❌ Error al abrir selector de documentos:', error);
            Alert.alert('Error', 'Error al abrir el selector de archivos. Intenta de nuevo.');
            onClose();
        }
    };

    return (
        <Modal visible={visible} transparent animationType="fade">
            <View style={styles.overlay}>
                <View style={styles.modal}>
                    <Text style={styles.title}>{modalTitle}</Text>

                    {/* Mensaje informativo temporal */}
                    {type === 'file' && (
                        <Text style={styles.infoText}>
                            📷 Por el momento solo se pueden subir fotos e imágenes.
                            {'\n'}📄 Los documentos PDF/Word estarán disponibles próximamente.
                        </Text>
                    )}

                    {/* Opciones para fotos */}
                    {(type === 'photo' || type === 'file') && (
                        <TouchableOpacity style={styles.button} onPress={handleCamera}>
                            <Text style={styles.buttonText}>Tomar Foto</Text>
                        </TouchableOpacity>
                    )}

                    {(type === 'photo' || type === 'file') && (
                        <TouchableOpacity style={styles.button} onPress={handleGallery}>
                            <Text style={styles.buttonText}>Elegir de Galería</Text>
                        </TouchableOpacity>
                    )}

                    {/* Opción para documentos (solo para type='file') - TEMPORALMENTE DESHABILITADO */}
                    {false && type === 'file' && (
                        <TouchableOpacity style={styles.button} onPress={handleDocuments}>
                            <Text style={styles.buttonText}>Seleccionar Archivo</Text>
                        </TouchableOpacity>
                    )}

                    <TouchableOpacity style={[styles.button, styles.cancelButton]} onPress={onClose}>
                        <Text style={styles.cancelButtonText}>Cancelar</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.3)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modal: {
        width: '85%',
        backgroundColor: 'white',
        borderRadius: vs(25),
        paddingVertical: vs(20),
        paddingHorizontal: vs(27),
        alignItems: 'center',
    },
    title: {
        fontSize: vs(18),
        fontWeight: 'bold',
        marginBottom: vs(25),
        textAlign: 'center',
    },
    infoText: {
        fontSize: vs(14),
        color: '#666',
        textAlign: 'center',
        marginBottom: vs(15),
        paddingHorizontal: vs(10),
        lineHeight: vs(20),
    },
    button: {
        backgroundColor: globalTheme.gradient[1],
        borderRadius: vs(25),
        paddingVertical: vs(8),
        marginBottom: vs(10),
        width: '100%',
        alignItems: 'center',
    },
    buttonText: {
        color: 'white',
        fontSize: vs(14),
        fontWeight: "600"
    },
    cancelButton: {
        backgroundColor: '#8797B8',
    },
    cancelButtonText: {
        color: '#ffffff',
        fontSize: vs(14),
        fontWeight: "600"
    },
});
