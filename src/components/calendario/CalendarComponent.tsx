import React, {useEffect, useState} from 'react';
import { Calendar, DateData, LocaleConfig } from 'react-native-calendars';
import { vs } from 'react-native-size-matters';
import { AntDesign } from '@expo/vector-icons';
import {View, Text} from 'react-native';
import '../../constants/localeCalendar';
import formatYMDWithOffset from "../../hooks/formatYMD";
import {borderRadius, globalTheme} from "../../constants/theme";

type DateRange = { start: string; end: string };

export default function CalendarComponent({markedDates, setCalendarDaySelected, setCalendarIdSelected, setDataRange}: {
    markedDates: Record<string, { dots: Array<{ key: string; color: string; selectedDotColor?: string; id: string }> }>;
    setCalendarDaySelected: (date: string) => void;
    setCalendarIdSelected: (date: string[]) => void;
    setDataRange: (range: DateRange) => void;
}) {

    const today = new Date();

    const [selectedDate, setSelectedDate] = useState<string>('');

    const [visibleMonth, setVisibleMonth] = useState<{ month: number; year: number }>({
        month: new Date().getMonth() + 1,
        year:  new Date().getFullYear()
    });
    const onDayPress = (day: DateData) => {

        const date = day.dateString;
        setSelectedDate(date);
        const dayDots = markedDates[date]?.dots ?? [];
        if (dayDots.length === 0) {
            setCalendarDaySelected(date);
            setCalendarIdSelected(['']);
        } else {
            // Extraes los IDs
            const ids = dayDots.map(d => d.id);
            console.log(ids)
            setCalendarDaySelected(date);
            setCalendarIdSelected(ids);
        }
    };

    useEffect(() => {
        const todayDate = new Date();
        const todayString = todayDate.toISOString().split('T')[0];

        const todayData = {
            dateString: todayString,
            day: todayDate.getDate(),
            month: todayDate.getMonth() + 1,
            year: todayDate.getFullYear(),
            timestamp: todayDate.getTime()
        };

        if (Object.keys(markedDates).length > 0) {
            onDayPress(todayData);
        }
    }, [markedDates]);


    const min   = new Date(today.getFullYear(), today.getMonth(), 1);


    const locale = LocaleConfig.locales[LocaleConfig.defaultLocale];


    return (
        <Calendar
            onDayPress={onDayPress}

            // Flechas
            renderArrow={(direction) => (

                    <AntDesign
                        name={direction === 'left' ? 'left' : 'right'}
                        size={20}
                        color="white"

                    />
            )}

            onPressArrowLeft={(subtractMonth) => {
                subtractMonth();
                setSelectedDate('')
            }}

            // Cuando pulsan la flecha derecha:
            onPressArrowRight={(addMonth) => {
                addMonth();
                setSelectedDate('')
            }}

            // Este callback te da el mes y año activo después de cambiar:
            onMonthChange={(date: DateData) => {
                setVisibleMonth({ month: date.month, year: date.year });
                const firstOfMonth = new Date(date.year, date.month - 1, 1);
                setDataRange({start: formatYMDWithOffset(firstOfMonth, 0, true), end:formatYMDWithOffset(firstOfMonth, 0, false)})
            }}

            // Header con año dinámico
            renderHeader={(date) => {
                const year      = date.getFullYear();
                const monthName = locale.monthNames[date.getMonth()] + ' ' + year;
                return (
                    <View style={{ width: vs(150) }}>
                        <Text style={{
                            fontSize: vs(18),
                            fontWeight: 'bold',
                            color: globalTheme.text_head,
                            paddingVertical: vs(10),
                            textAlign: 'center',
                        }}>
                            {monthName}
                        </Text>
                        <View style={{
                            height: 3,
                            backgroundColor: globalTheme.background,
                            width: '150%',
                            alignSelf: 'center',
                            borderRadius: 30,
                            zIndex: 10,
                        }}/>
                    </View>
                );
            }}

            // Rango de fechas dinámico
            minDate={formatYMDWithOffset(min, 0, true)}
            //maxDate={formatYMD(max)}

            hideExtraDays
            hideArrows={false}
            disableAllTouchEventsForDisabledDays
            markingType="multi-dot"
            markedDates={{
                ...markedDates,
                ...(selectedDate && {
                    [selectedDate]: {
                        ...(markedDates[selectedDate] || {}),
                        selected: true,
                        selectedColor: '#02415F',
                    },
                }),
            }}
            monthFormat="MMMM"

            style={{
                width: '90%',
                alignSelf: 'center',
                borderRadius: borderRadius,
                backgroundColor: globalTheme.container_translucent,
                marginTop: vs(20),
                paddingVertical: vs(10),
            }}

            theme={{
                calendarBackground: 'transparent',
                textSectionTitleColor: globalTheme.text_head,
                dayTextColor: globalTheme.text_head,
                todayTextColor: globalTheme.text_contrast,
                textDayFontWeight: 'bold',
                textDayHeaderFontWeight: 'bold',
            }}
        />
    );
}
