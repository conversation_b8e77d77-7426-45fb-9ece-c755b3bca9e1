import {StyleSheet, Text, View} from "react-native";
import {vs} from "react-native-size-matters";
import {Ionicons} from "@expo/vector-icons";
import COLORS from "../../constants/colors";
import {borderRadius, globalTheme} from "../../constants/theme";
import {LocaleConfig} from "react-native-calendars";


interface Evento {
    id: string;
    description: string;
    end_date: string;
    start_date: string;
    image: string;
    title: string;
}

type Props = {
    eventos: Evento[]
}
export default function TodayEvent({eventos}: Props){
    const locale = LocaleConfig.locales[LocaleConfig.defaultLocale];

    const convertirFecha = (fecha: string): string => {
        const soloFecha = fecha.split(' ')[0];
        const [anio, mes, dia] = soloFecha.split('-');
        const nombreMes = locale.monthNames[Number(mes) - 1];
        return `${dia} DE ${nombreMes} DE ${anio}`;
    };

    return(
        <View style={styles.container}>


            {eventos[0].description === 'NO HAY EVENTOS DISPONIBLES' ? (
                <>
                    <View style={styles.header}>
                        <Text style={styles.headerText}>{convertirFecha(eventos[0].start_date)}</Text>
                    </View>
                    <View style={styles.separator}/>
                    <View style={[styles.details, {alignItems: 'center'}]}>
                        <Text style={styles.detailsText}>{eventos[0].description}</Text>
                    </View>
                </>
            ) : (
                <>
                    <View style={styles.header}>
                        <Text style={styles.headerText}>{convertirFecha(eventos[eventos.length - 1].start_date)}</Text>
                    </View>
                    <View style={styles.separator}/>
                    <View style={styles.details}>
                        {eventos.map((item: Evento, id) => (
                            <View key={id} style={{flexDirection: 'row', alignItems: 'center'}}>
                                <Ionicons name={'ellipse'} size={vs(13)} color={COLORS[parseInt(item.id, 10) % COLORS.length]}/>
                                <Text key={item.id} style={[styles.detailsText, {}]}>{item.description.toUpperCase()} {item.start_date.slice(11,16)} HRS.</Text>
                            </View>
                        ))}
                    </View>
                </>
            )}

        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: globalTheme.container_translucent,
        width: '90%',
        alignSelf: 'center',
        borderRadius: borderRadius,
        paddingVertical: vs(5),
        marginVertical: vs(15)
    },
    header: {
        alignItems: 'center',
        marginVertical: vs(10)
    },
    headerText: {
        fontSize: vs(16),
        color: globalTheme.text_head,
        fontWeight: "800"
    },
    details: {
        alignItems: "flex-start",
        margin: vs(20)
    },
    detailsText: {
        fontSize: vs(14),
        color: '#FFF',
        marginLeft: vs(3)
    },
    separator: {
        height: 2,
        borderRadius: 3,
        backgroundColor: globalTheme.background,
        width: '80%',
        alignSelf: "center",

    },
});