// EventList.tsx (Componente Acordeón)
import React from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { mvs, vs } from 'react-native-size-matters';
import Animated, {
    measure,
    runOnUI,
    useAnimatedRef,
    useAnimatedStyle,
    useDerivedValue,
    useSharedValue,
    withTiming,
    interpolate,
    Extrapolation
} from 'react-native-reanimated';
import { LocaleConfig } from 'react-native-calendars';
import RotableChevronDown from './RotableChevronDown';
import COLORS from "../../constants/colors";
import {Ionicons} from "@expo/vector-icons";
import {borderRadius, globalTheme} from "../../constants/theme";

interface Evento {
    id: string;
    description: string;
    end_date: string;
    start_date: string;
    image: string;
    title: string;
}

type Props = {
    numeroEventos: number;
    eventos: Evento[];
};

const EventList = ({ numeroEventos, eventos }: Props) => {

    const locale = LocaleConfig.locales[LocaleConfig.defaultLocale];

    const listRef = useAnimatedRef<Animated.View>();
    const open = useSharedValue(false);
    const heightValue = useSharedValue(0);
    const progress = useDerivedValue(() =>
        open.value ? withTiming(1, { duration: 300 }) : withTiming(0, { duration: 200 })
    );

    const heightAnimationStyle = useAnimatedStyle(() => ({
        height: interpolate(
            progress.value,
            [0, 1],
            [0, heightValue.value],
            Extrapolation.CLAMP
        ),
        overflow: 'hidden',
    }));

    const handleToggle = () => {
        runOnUI(() => {
            'worklet';
            const measured = measure(listRef);
            if (measured) {
                if (heightValue.value === 0) {
                    heightValue.value = measured.height;
                }
                open.value = !open.value;
            }
        })();
    };

    const convertirFecha = (fecha: string): string => {
        const soloFecha = fecha.split(' ')[0];
        const [anio, mes, dia] = soloFecha.split('-');
        const nombreMes = locale.monthNames[Number(mes) - 1];
        return `${dia} DE ${nombreMes} DE ${anio} ${fecha.slice(11,16)} hrs.`;
    };


    return (
        <Animated.View style={styles.container}>
            <View style={styles.header}>
                <View style={styles.titleContainer}>
                    <View style={{width: '70%'}}>
                        <Text style={styles.title}>
                            TIENES {numeroEventos} EVENTOS PRÓXIMOS
                        </Text>
                    </View>
                    <TouchableOpacity onPress={handleToggle} style={styles.closeButton}>
                        <Feather name="x" size={mvs(20)} color="white" />
                    </TouchableOpacity>
                </View>

                <TouchableOpacity onPress={handleToggle} style={styles.toggleButton}>
                    <Text style={styles.toggleText}>Ver eventos</Text>
                    <RotableChevronDown progress={progress} />
                </TouchableOpacity>
            </View>

            <Animated.View style={heightAnimationStyle}>
                <Animated.View
                    ref={listRef}
                    style={styles.content}
                    onLayout={() => {
                        runOnUI(() => {
                            'worklet';
                            const measured = measure(listRef);
                            if (measured && heightValue.value === 0) {
                                heightValue.value = measured.height;
                            }
                        })();
                    }}
                >
                    {eventos.map((evento) => (
                        <View key={String(evento.id)} style={styles.eventItem}>
                            <Text style={styles.eventDetails}>{evento.description}</Text>
                            <View style={styles.dateContainer}>
                                <Ionicons name={'ellipse'} size={vs(13)} color={COLORS[parseInt(evento.id, 10) % COLORS.length]}/>
                                <Text style={styles.eventDate}>
                                    {convertirFecha(evento.start_date)}
                                </Text>
                            </View>
                            <View style={styles.separator} />
                        </View>
                    ))}
                </Animated.View>
            </Animated.View>
        </Animated.View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: globalTheme.container_translucent,
        borderRadius: borderRadius,
        width: '90%',
        alignSelf: 'center',
        overflow: 'hidden',
    },
    header: {
        padding: vs(10),
        backgroundColor: globalTheme.button_bad
    },
    titleContainer: {
        flexDirection: 'column',
        alignItems: 'center',
        marginBottom: vs(3),
    },
    title: {
        color: globalTheme.text_head,
        fontSize: vs(16),
        fontWeight: 'bold',
        textAlign: 'center',
    },
    closeButton: {
        position: "absolute",
        zIndex: 2,
        right: 0,
        top: 0
    },
    toggleButton: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        padding: vs(5),
    },
    toggleText: {
        color: globalTheme.text_head,
        fontSize: vs(14),
        marginRight: vs(3),
        textDecorationLine: 'underline',
    },
    content: {
        position: 'absolute',
        width: '100%',
        paddingBottom: vs(20)
    },
    eventItem: {
        paddingHorizontal: vs(20),
        paddingTop: vs(20),
    },
    eventDetails: {
        color: 'white',
        fontSize: vs(14),
        fontWeight: '600',
        textTransform: 'uppercase',
    },
    dateContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: vs(10),
    },
    eventDate: {
        color: globalTheme.text_head,
        fontSize: vs(14),
        fontWeight: '500',
    },
    separator: {
        height: 1,
        backgroundColor: globalTheme.container_translucent,
    },
});

export default EventList;