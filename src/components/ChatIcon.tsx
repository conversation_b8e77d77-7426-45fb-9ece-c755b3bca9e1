import {Dimensions, Pressable, StyleSheet, View} from "react-native";
import {Ionicons} from "@expo/vector-icons";
import {vs} from "react-native-size-matters";
import {useNavigation} from "@react-navigation/native";
import {NativeStackNavigationProp} from "@react-navigation/native-stack";
import {RootStackParamList} from "../navigation/AppNavigator";
import {globalTheme} from "../constants/theme";

const {height} = Dimensions.get("window")

const ChatIcon= () => {

    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList, 'ChatScreen'>>();

    return(
        <Pressable
            onPress={() => navigation.navigate('ChatScreen')}
            style={styles.container}
        >
            <Ionicons
                name={'chatbubble-ellipses'}
                style={styles.icon}
            />
        </Pressable>
    )
}

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        right: height * .01,
        bottom: height * .01,
        width: vs(50),
        height: vs(50),
        justifyContent: "center",
        alignItems: "center",
        borderRadius: vs(50),
        backgroundColor: globalTheme.gradient[0],
        borderColor: "#fff",
        borderWidth: 1
    },
    icon: {
        color: "#fff",
        fontSize: vs(30),

    }
})

export default ChatIcon;