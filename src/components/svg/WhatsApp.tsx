import * as React from "react";
import Svg, { Path,G } from "react-native-svg";
function SVGComponent  (props) {
    return(
        <Svg
            width={33}
            height={33}
            viewBox="0 0 33 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <Path
                d="M28.8509 0.221069H3.68897C1.7181 0.221069 0.120605 1.81856 0.120605 3.78943V28.9514C0.120605 30.9223 1.7181 32.5198 3.68897 32.5198H28.8509C30.8218 32.5198 32.4193 30.9223 32.4193 28.9514V3.78943C32.4193 1.81856 30.8218 0.221069 28.8509 0.221069ZM17.8571 26.7884C15.6272 27.1104 13.4987 26.7551 11.4872 25.7361C11.3111 25.6469 11.1593 25.6282 10.9672 25.6795C9.2831 26.1288 7.59646 26.5671 5.91047 27.008C5.8294 27.029 5.74607 27.0409 5.62721 27.0645C5.83134 26.312 6.02513 25.5907 6.22247 24.8701C6.4996 23.8576 6.77543 22.8447 7.06192 21.8347C7.11037 21.6635 7.09099 21.5292 7.00733 21.3745C3.88922 15.5852 6.61587 8.49535 12.8069 6.28483C18.9262 4.10014 25.6556 8.03768 26.7557 14.4464C27.7709 20.3593 23.7552 25.937 17.8571 26.789V26.7884Z"
                fill="black"
            />
            <G transform="translate(7,7)">
                <Path
                    d="M7.61782 0.647744C3.72777 1.3735 0.710427 4.83882 0.519541 8.78249C0.426521 10.702 0.886132 12.4694 1.92227 14.0853C2.02531 14.2461 2.04921 14.385 1.99333 14.5691C1.85025 15.0387 1.72945 15.5152 1.60058 15.989C1.46266 16.4957 1.32539 17.0028 1.19006 17.5015C1.2637 17.5487 1.30698 17.5203 1.3522 17.5086C2.32084 17.2541 3.29012 17.0035 4.25714 16.7428C4.42768 16.697 4.56269 16.7138 4.71676 16.8061C6.65662 17.9699 8.74117 18.3484 10.9691 17.9482C15.6508 17.1072 18.8894 12.3851 17.986 7.71341C17.0419 2.83243 12.4998 -0.263079 7.61847 0.647744H7.61782ZM14.7497 12.4981C14.6321 13.6312 14.1341 14.2348 12.7401 14.6486C12.5359 14.709 12.3228 14.72 12.138 14.7145C11.65 14.7154 11.2159 14.5921 10.7834 14.4529C8.20372 13.6221 6.30908 11.9533 4.89052 9.68298C4.38053 8.86712 3.8996 8.04608 3.80238 7.06808C3.69353 5.97541 4.06432 5.05845 4.89182 4.33561C5.25001 4.02263 5.91891 3.96547 6.34041 4.18607C6.51967 4.27973 6.64434 4.42152 6.71475 4.60918C6.97831 5.31264 7.23702 6.01772 7.50219 6.72054C7.58746 6.94663 7.52157 7.14237 7.38527 7.31452C7.19988 7.54836 6.99285 7.76476 6.80357 7.99537C6.53582 8.32159 6.53065 8.45983 6.73833 8.82739C7.51511 10.203 8.61844 11.2327 10.026 11.942C10.5108 12.1861 10.6568 12.1645 11.0263 11.7582C11.266 11.4943 11.5043 11.2278 11.723 10.9468C11.8864 10.7366 12.0731 10.6842 12.304 10.7908C13.0834 11.15 13.8302 11.5715 14.5798 11.9878C14.7897 12.1044 14.771 12.304 14.7507 12.4985L14.7497 12.4981Z"
                    fill="black"
                />
            </G>
        </Svg>
    );
};
export default SVGComponent;
