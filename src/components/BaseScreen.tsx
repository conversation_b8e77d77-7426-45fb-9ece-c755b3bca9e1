import React, {useCallback, useEffect} from 'react';
import {
    View,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    StyleProp,
    ViewStyle,
    Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {globalTheme} from '../constants/theme';
import { SafeAreaView } from 'react-native-safe-area-context';
import ChatIcon from "./ChatIcon";
import changeNavigationBarColor from "react-native-navigation-bar-color";
import {useFocusEffect} from "@react-navigation/native";

type Props = {
    children: React.ReactNode;
    scroll?: boolean;
    style?: StyleProp<ViewStyle>;
    keyboardAdjust?: boolean;
    chat?: boolean
    navColor?: string;
};

export default function BaseScreen({ 
    children, 
    scroll = true, 
    style,
    keyboardAdjust = true,
    chat = false,
    navColor = '#ffffff'
}: Props) {
    const { width } = Dimensions.get('window');
    const Content = scroll ? ScrollView : View;

    useFocusEffect(
        useCallback(() => {
            changeNavigationBarColor(navColor, true);
        }, [navColor])
    );


    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: '#02415F' }} edges={['top']}>
            <View style={StyleSheet.absoluteFill}>
                <LinearGradient
                    colors={[globalTheme.gradient[0], globalTheme.gradient[1]]}
                    style={StyleSheet.absoluteFillObject}
                />
            </View>

            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                style={{ flex: 1 }}
                enabled={keyboardAdjust}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
            >
                <View style={styles.wrapper}>
                    <Content
                        keyboardShouldPersistTaps="handled"
                        contentContainerStyle={[
                            styles.contentContainer,
                            style,
                            scroll ? styles.scrollContent : null
                        ]}
                    >
                        {children}
                    </Content>
                    {chat && (
                        <ChatIcon/>
                    )}
                </View>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    wrapper: {
        flex: 1,
    },
    contentContainer: {
        width: '100%',
    },
    scrollContent: {
        flexGrow: 1,
    }
});
