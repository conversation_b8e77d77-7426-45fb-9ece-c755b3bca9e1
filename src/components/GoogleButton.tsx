import {Text, TouchableOpacity, StyleSheet} from "react-native";
import GoogleSVG from "./svg/Google";
import React from "react";
import {ms, vs} from "react-native-size-matters";
import * as <PERSON> from 'expo-auth-session/providers/google';

export default function GoogleButton(){

    const [request, response, promptAsync] = Google.useAuthRequest({
        androidClientId: '539918208542-2cirajv743tmetik99omvbh33cl3rd2g.apps.googleusercontent.com',

    })


    return(
        <TouchableOpacity
            style={styles.googleButton}
            onPress={() => promptAsync().catch((e) => {
                console.log("Error al iniciar sesion: ", e)
            })}
        >
            <Text style={[styles.buttonText, {color: 'white'}]}>ACCESO CON GOOGLE</Text>
            <GoogleSVG/>
        </TouchableOpacity>
    )
}

const styles = StyleSheet.create({
    googleButton: {
        backgroundColor: '#292929',
        borderRadius: ms(20),
        padding: ms(12),
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: vs(145)
    },
    buttonText: {
        color: '#ffffff',
        fontWeight: '900',
        fontSize: vs(10),
        flexWrap: "wrap",
        textAlign: "center"
    },
})