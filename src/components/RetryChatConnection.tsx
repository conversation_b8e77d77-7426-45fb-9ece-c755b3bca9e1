import {Pressable, StyleSheet, Text, TouchableOpacity, View} from "react-native";
import {Ionicons} from "@expo/vector-icons";
import {vs} from "react-native-size-matters";
import {borderRadius, globalTheme} from "../constants/theme";

interface RetryChatConnectionProps {
    retry?: () => void
}

const RetryChatConnection = ({retry}:RetryChatConnectionProps) => {
    return (
        <TouchableOpacity
            onPress={() => retry()}
            style={styles.container}>
            <Text>Conexión perdida, recargar</Text>
            <Ionicons name={'reload'} size={vs(20)} color="black"/>
        </TouchableOpacity>
    )
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: 'white',
        position: 'absolute',
        zIndex: 5,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: vs(12),
        paddingVertical: vs(5),
        borderRadius: borderRadius,
        borderWidth: 1,
        borderColor: globalTheme.gradient[1],
        marginTop: vs(150),
    }
})
export default RetryChatConnection;