import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {borderRadius, globalTheme} from "../../constants/theme";
import {vs} from "react-native-size-matters";
import PhotoPickerModal from '../common/PhotoPickerModal';
import FilePickerModal from '../common/FilePickerModal';
import ImageViewerModal from '../common/ImageViewerModal';
import AuthenticatedFile from '../common/AuthenticatedFile';
import { API_URL } from '../../constants/config';

export interface FormFieldOption {
  label: string;
  value: string | number;
}

export interface FormFieldProps {
  id: string;
  type: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'email' | 'phone' | 'number' | 'file' | 'photo';
  label: string;
  value: any;
  placeholder?: string;
  required?: boolean;
  options?: FormFieldOption[];
  multiline?: boolean;
  numberOfLines?: number;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  onChangeValue: (fieldId: string, value: any) => void;
  error?: string;
  disabled?: boolean;
  userId?: number; // Para construir URLs de archivos del servidor
}

// Helper function to build image URL using the secure endpoint
const getImageUrl = (uri: string, userId?: number): string => {
  // Validación defensiva: asegurar que uri sea un string
  if (!uri || typeof uri !== 'string') return '';

  // Si es una URI local (recién seleccionada), devolverla tal como está
  if (uri.startsWith('file://')) {
    return uri;
  }

  // Si ya es una URL completa, devolverla tal como está
  if (uri.startsWith('http://') || uri.startsWith('https://')) {
    return uri;
  }

  // Si es una ruta del servidor, construir URL usando el endpoint seguro
  if (uri.startsWith('/uploads/forms/')) {
    // Extraer userId y filename de la ruta: /uploads/forms/{userId}/{filename}
    const pathParts = uri.split('/');
    if (pathParts.length >= 5) {
      const extractedUserId = pathParts[3];
      const filename = pathParts[4];

      // Construir URL del endpoint seguro
      const baseUrl = API_URL.replace(/\/$/, '');
      return `${baseUrl}/files/${extractedUserId}/${filename}`;
    }
  }

  // Si tenemos userId y es solo el nombre del archivo, construir la URL
  if (userId && !uri.includes('/')) {
    const baseUrl = API_URL.replace(/\/$/, '');
    return `${baseUrl}/files/${userId}/${uri}`;
  }

  return uri;
};

export default function FormField({
  id,
  type,
  label,
  value,
  placeholder,
  required = false, // Mantener para compatibilidad futura
  options = [],
  multiline = false,
  numberOfLines = 1,
  keyboardType = 'default',
  onChangeValue,
  error,
  disabled = false,
  userId,
}: FormFieldProps) {
  const [showModal, setShowModal] = useState(false);
  const [showPhotoModal, setShowPhotoModal] = useState(false);
  const [showFileModal, setShowFileModal] = useState(false);
  const [showImageViewer, setShowImageViewer] = useState(false);

  const handleTextChange = (text: string) => {
    onChangeValue(id, text);
  };

  const handleSelectOption = (selectedValue: any) => {
    onChangeValue(id, selectedValue);
    setShowModal(false);
  };

  const handleCheckboxToggle = (optionValue: any) => {
    const currentValues = Array.isArray(value) ? value : [];
    const newValues = currentValues.includes(optionValue)
      ? currentValues.filter((v: any) => v !== optionValue)
      : [...currentValues, optionValue];
    onChangeValue(id, newValues);
  };

  const handlePhotoSelect = (uri: string) => {
    // console.log('📸 Foto seleccionada para campo:', id, uri);
    onChangeValue(id, uri);
    setShowPhotoModal(false);
  };

  const handleRemoveFile = () => {
    // console.log('🗑️ Removiendo archivo del campo:', id);
    onChangeValue(id, '');
  };

  const renderTextInput = () => {
    // Determinar el tipo de teclado basado en el tipo de campo
    let inputKeyboardType = keyboardType;
    if (type === 'email') {
      inputKeyboardType = 'email-address';
    } else if (type === 'phone') {
      inputKeyboardType = 'phone-pad';
    } else if (type === 'number') {
      inputKeyboardType = 'numeric';
    }

    // console.log(`Rendering text input for ${id}, type: ${type}, keyboardType: ${inputKeyboardType}`);

    return (
      <TextInput
        value={value || ''}
        onChangeText={handleTextChange}
        placeholder={placeholder || `Ingresa ${label.toLowerCase()}`}
        style={[
          styles.textInput,
          multiline && styles.textAreaInput,
          error && styles.inputError,
          disabled && styles.inputDisabled,
        ]}
        multiline={multiline || type === 'textarea'}
        numberOfLines={type === 'textarea' ? 4 : numberOfLines}
        textAlignVertical={multiline || type === 'textarea' ? 'top' : 'center'}
        keyboardType={inputKeyboardType}
        editable={!disabled}
      />
    );
  };

  const renderSelectInput = () => {
    const selectedOption = options.find(opt => opt.value === value);
    return (
      <>
        <TouchableOpacity
          style={[
            styles.selectInput,
            error && styles.inputError,
            disabled && styles.inputDisabled,
          ]}
          onPress={() => !disabled && setShowModal(true)}
          disabled={disabled}
        >
          <Text style={[
            styles.selectText,
            !selectedOption && styles.placeholderText,
            disabled && styles.disabledText,
          ]}>
            {selectedOption ? selectedOption.label : (placeholder || 'Selecciona una opción')}
          </Text>
          <Ionicons 
            name="chevron-down" 
            size={20} 
            color={disabled ? '#999' : '#666'} 
          />
        </TouchableOpacity>

        <Modal
          visible={showModal}
          transparent
          animationType="fade"
          onRequestClose={() => setShowModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{label}</Text>
                <TouchableOpacity
                  onPress={() => setShowModal(false)}
                  style={styles.closeButton}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              <FlatList
                data={options}
                keyExtractor={(item) => item.value.toString()}
                renderItem={({ item, index }) => (
                  <TouchableOpacity
                      key={index}
                    style={[
                      styles.optionItem,
                      item.value === value && styles.selectedOption,
                    ]}
                    onPress={() => handleSelectOption(item.value)}
                  >
                    <Text style={[
                      styles.optionText,
                      item.value === value && styles.selectedOptionText,
                    ]}>
                      {item.label}
                    </Text>
                    {item.value === value && (
                      <Ionicons name="checkmark" size={20} color="#1698BF" />
                    )}
                  </TouchableOpacity>
                )}
              />
            </View>
          </View>
        </Modal>
      </>
    );
  };

  const renderRadioInput = () => (
    <View style={styles.radioContainer}>
      {options.map((option) => (
        <TouchableOpacity
          key={option.value}
          style={styles.radioOption}
          onPress={() => !disabled && onChangeValue(id, option.value)}
          disabled={disabled}
        >
          <View style={[
            styles.radioCircle,
            value === option.value && styles.radioSelected,
            disabled && styles.radioDisabled,
          ]}>
            {value === option.value && <View style={styles.radioInner} />}
          </View>
          <Text style={[
            styles.radioLabel,
            disabled && styles.disabledText,
          ]}>
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderCheckboxInput = () => {
    const currentValues = Array.isArray(value) ? value : [];

    // Validación defensiva para opciones
    if (!Array.isArray(options) || options.length === 0) {
      console.warn('⚠️ Checkbox options are not valid:', options);
      return (
        <View style={styles.checkboxContainer}>
          <Text style={styles.errorText}>No hay opciones disponibles</Text>
        </View>
      );
    }

    // Si está deshabilitado (formulario respondido), mostrar todas las opciones con estado visual
    if (disabled) {
      return (
        <View style={styles.checkboxContainer}>
          {options.map((option, index) => {
            // Validación defensiva para cada opción
            if (!option || typeof option !== 'object') {
              console.warn('⚠️ Invalid option at index', index, ':', option);
              return null;
            }

            const optionValue = option.value !== undefined ? option.value : option.label;
            const optionLabel = option.label !== undefined ? option.label : option.value;

            if (optionValue === undefined || optionLabel === undefined) {
              console.warn('⚠️ Option missing value or label at index', index, ':', option);
              return null;
            }

            const isSelected = currentValues.includes(optionValue);

            return (
              <View
                key={`readonly-${optionValue}-${index}`}
                style={[
                  styles.checkboxOption,
                  styles.checkboxOptionReadonly
                ]}
              >
                <View style={[
                  styles.checkbox,
                  isSelected && styles.checkboxSelected,
                  styles.checkboxDisabled,
                ]}>
                  {isSelected && (
                    <Ionicons name="checkmark" size={16} color="#000" />
                  )}
                </View>
                <Text style={[
                  styles.checkboxLabel,
                  styles.disabledText,
                  isSelected && styles.selectedCheckboxLabel
                ]}>
                  {optionLabel}
                </Text>
              </View>
            );
          })}
        </View>
      );
    }



    // Modo normal (habilitado) - mostrar todas las opciones
    return (
      <View style={styles.checkboxContainer}>
        {options.map((option, index) => {
          // Validación defensiva para cada opción
          if (!option || typeof option !== 'object') {
            console.warn('⚠️ Invalid option at index', index, ':', option);
            return null;
          }

          const optionValue = option.value !== undefined ? option.value : option.label;
          const optionLabel = option.label !== undefined ? option.label : option.value;

          if (optionValue === undefined || optionLabel === undefined) {
            console.warn('⚠️ Option missing value or label at index', index, ':', option);
            return null;
          }

          const isChecked = currentValues.includes(optionValue);
          return (
            <TouchableOpacity
              key={`${id}-${optionValue}-${index}`}
              style={styles.checkboxOption}
              onPress={() => !disabled && handleCheckboxToggle(optionValue)}
              disabled={disabled}
            >
              <View style={[
                styles.checkbox,
                isChecked && styles.checkboxSelected,
                disabled && styles.checkboxDisabled,
              ]}>
                {isChecked && (
                  <Ionicons name="checkmark" size={16} color="#000" />
                )}
              </View>
              <Text style={[
                styles.checkboxLabel,
                disabled && styles.disabledText,
              ]}>
                {optionLabel}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  const renderFileInput = () => {
    // Validación defensiva: solo procesar si es un campo de archivo
    if (!['file', 'photo'].includes(type)) {
      console.warn('⚠️ renderFileInput called for non-file field type:', type);
      return null;
    }

    const hasFile = value && typeof value === 'string' && value.length > 0;
    const isImage = hasFile && typeof value === 'string' && (value.startsWith('file://') || value.startsWith('http') || value.startsWith('/uploads'));

    return (
      <View style={styles.fileContainer}>
        {hasFile ? (
          <View style={styles.filePreviewContainer}>
            <AuthenticatedFile
              uri={getImageUrl(value, userId)}
              onPress={isImage ? () => setShowImageViewer(true) : undefined}
              showPreview={true}
            />

            {!disabled && (
              <TouchableOpacity
                style={styles.removeFileButton}
                onPress={handleRemoveFile}
              >
                <Ionicons name="close-circle" size={24} color="#EF4444" />
              </TouchableOpacity>
            )}
          </View>
        ) : (
          !disabled && (
            <TouchableOpacity
              style={[
                styles.fileSelectButton,
                error && styles.inputError,
              ]}
              onPress={() => {
                if (type === 'photo') {
                  setShowPhotoModal(true);
                } else {
                  setShowFileModal(true);
                }
              }}
            >
              <Ionicons
                name={type === 'photo' ? 'camera' : 'document'}
                size={24}
                color={globalTheme.gradient[1]}
              />
              <Text style={styles.fileSelectText}>
                {placeholder || (type === 'photo' ? 'Seleccionar foto' : 'Seleccionar archivo')}
              </Text>
            </TouchableOpacity>
          )
        )}

        {/* Mostrar mensaje cuando está deshabilitado y no hay archivo */}
        {disabled && !hasFile && (
          <View style={styles.noFileContainer}>
            <Ionicons name="document-outline" size={24} color="#999" />
            <Text style={styles.noFileText}>
              No se adjuntó ningún archivo
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderField = () => {
    // console.log(`🔧 Rendering field type: ${type} with id: ${id}, value:`, value);
    switch (type) {
      case 'text':
      case 'email':
      case 'phone':
      case 'number':
        return renderTextInput();
      case 'textarea':
        return renderTextInput();
      case 'select':
        return renderSelectInput();
      case 'radio':
        return renderRadioInput();
      case 'checkbox':
        return renderCheckboxInput();
      case 'file':
      case 'photo':
        return renderFileInput();
      default:
        // console.warn(`Unsupported field type: ${type}`);
        return (
          <View style={styles.unsupportedField}>
            <Text style={styles.unsupportedText}>
              Tipo de campo no soportado: {type}
            </Text>
          </View>
        );
    }
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.label, error && styles.labelError]}>
        {label}{/*
        {required && <Text style={styles.required}> *</Text>}*/}
      </Text>
      {renderField()}
      {error && (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={16} color="#EF4444" />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {/* Photo Picker Modal */}
      <PhotoPickerModal
        visible={showPhotoModal}
        onClose={() => setShowPhotoModal(false)}
        onPick={handlePhotoSelect}
      />

      {/* File Picker Modal */}
      <FilePickerModal
        visible={showFileModal}
        onClose={() => setShowFileModal(false)}
        onPick={handlePhotoSelect}
        type="file"
        title="Seleccionar archivo"
      />

      {/* Image Viewer Modal */}
      <ImageViewerModal
        visible={showImageViewer}
        imageUri={getImageUrl(value || '', userId)}
        onClose={() => setShowImageViewer(false)}
        title={label}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: vs(20),
  },
  label: {
    fontSize: vs(14),
    fontWeight: '600',
    color: globalTheme.text_head,
    marginBottom: 8,
    textTransform: "uppercase"
  },
  labelError: {
  },
  required: {
    color: globalTheme.text_error,
  },
  textInput: {
    backgroundColor: globalTheme.input,
    borderRadius: borderRadius,
    borderColor: '#E5E7EB',
    padding: vs(10),
    fontSize: 16,
    color: '#333',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 3,
  },
  textAreaInput: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: '#EF4444',
  },
  inputDisabled: {
    backgroundColor: '#F3F4F6',
    color: '#9CA3AF',
  },
  selectInput: {
    backgroundColor: globalTheme.input,
    borderRadius: borderRadius,
    borderColor: '#E5E7EB',
    padding: vs(10),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectText: {
    fontSize: vs(14),
    color: globalTheme.text_contrast,
    flex: 1,
  },
  placeholderText: {
    color: globalTheme.text_placeholder,
  },
  disabledText: {
    color: '#9CA3AF',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: globalTheme.input,
    borderTopLeftRadius: borderRadius,
    borderTopRightRadius: borderRadius,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: vs(15),
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: vs(16),
    fontWeight: '600',
    color: globalTheme.text_contrast,
  },
  closeButton: {
    padding: 4,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  selectedOption: {
    backgroundColor: '#EBF8FF',
  },
  optionText: {
    fontSize: 16,
    color: globalTheme.text_contrast,
  },
  selectedOptionText: {
    color: '#1698BF',
    fontWeight: '500',
  },
  radioContainer: {
    gap: 12,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  radioCircle: {
    width: vs(15),
    height: vs(15),
    borderRadius: vs(8),
    borderWidth: 2,
    borderColor: '#D1D5DB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioSelected: {
    borderColor: globalTheme.gradient[1],
  },
  radioDisabled: {
    borderColor: '#D1D5DB',
    backgroundColor: '#F3F4F6',
  },
  radioInner: {
    width: vs(8),
    height: vs(8),
    borderRadius: vs(8),
    backgroundColor: globalTheme.gradient[1],
  },
  radioLabel: {
    fontSize: vs(14),
    color: globalTheme.text_head,
  },
  checkboxContainer: {
    gap: 12,
  },
  checkboxOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  checkbox: {
    width: vs(20),
    height: vs(20),
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: globalTheme.gradient[1],
    borderColor: globalTheme.gradient[1],
  },
  checkboxDisabled: {
    borderColor: '#D1D5DB',
    backgroundColor: '#F3F4F6',
  },
  checkboxLabel: {
    fontSize: 16,
    color: globalTheme.text_head,
  },
  unsupportedField: {
    backgroundColor: '#FEF3C7',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#F59E0B',
  },
  unsupportedText: {
    color: '#92400E',
    fontSize: 14,
    fontStyle: 'italic',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    gap: 6,
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
  },
  // Estilos para campos de archivo/foto
  fileContainer: {
    marginTop: 4,
  },
  fileSelectButton: {
    backgroundColor: globalTheme.input,
    borderRadius: borderRadius,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderStyle: 'dashed',
    padding: vs(16),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    minHeight: vs(60),
  },
  fileSelectText: {
    fontSize: vs(14),
    color: globalTheme.text_contrast,
    textAlign: 'center',
  },
  filePreviewContainer: {
    backgroundColor: globalTheme.input,
    borderRadius: borderRadius,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    padding: vs(12),
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  imagePreviewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  imagePreview: {
    width: vs(50),
    height: vs(50),
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  fileInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: vs(14),
    fontWeight: '500',
    color: globalTheme.text_head,
    marginBottom: 2,
  },
  fileSize: {
    fontSize: vs(12),
    color: globalTheme.text_placeholder,
  },
  removeFileButton: {
    padding: 4,
  },
  // Estilos para visualización de archivos
  imagePreviewTouchable: {
    position: 'relative',
    borderRadius: 8,
    overflow: 'hidden',
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    padding: 4,
    margin: 4,
  },
  documentPreview: {
    position: 'relative',
    padding: 4,
    borderRadius: 8,
    backgroundColor: 'rgba(22, 152, 191, 0.1)',
  },
  viewIcon: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: 'rgba(22, 152, 191, 0.8)',
    borderRadius: 8,
    padding: 2,
  },
  noFileContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: borderRadius,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderStyle: 'dashed',
    padding: vs(16),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    minHeight: vs(60),
  },
  noFileText: {
    fontSize: vs(14),
    color: '#9CA3AF',
    textAlign: 'center',
    fontStyle: 'italic',
  },

  // Estilos para checkbox en modo solo lectura
  checkboxOptionReadonly: {
    opacity: 1, // Mantener opacidad normal para mejor visibilidad
  },
  selectedCheckboxLabel: {
    fontWeight: '600',
    color: globalTheme.text_head,
  },
});
