// src/components/profile/ProfileActions.tsx
import React from 'react';
import {View, TouchableOpacity, Text} from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import {profileStyle as styles} from '../../styles/profileStyles';
import {vs} from "react-native-size-matters";

interface Props {
    isEditing: boolean;
    onEditToggle: () => void;
    onSaveProfile: () => void;
    onChangePassword?: () => void;
    onViewBeneficiaries?: () => void;
    onViewCredential?: () => void;
}

export default function ProfileActions({
                                           isEditing,
                                           onEditToggle,
                                           onSaveProfile,
                                           onChangePassword,
                                           onViewBeneficiaries,
                                           onViewCredential,
                                       }: Props) {
    const handlePrimaryAction = () => {
        isEditing ? onSaveProfile() : onEditToggle();
    };

    return (
        <>

            <View style={styles.buttonRow}>
                <TouchableOpacity
                    style={[
                        styles.updateButton,
                        isEditing && {backgroundColor: '#1ABC9C'}
                    ]}
                    onPress={handlePrimaryAction}
                >
                    <Ionicons name="repeat-outline" size={vs(18)} color="#fff"/>
                    <Text style={styles.buttonText}>
                        {isEditing ? 'GUARDAR' : 'EDITAR'}
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.passwordButton} onPress={onChangePassword}>
                    <Ionicons name="key-outline" size={vs(18)} color="#fff"/>
                    <Text style={styles.buttonText}>CAMBIAR CONTRASEÑA</Text>
                </TouchableOpacity>

            </View>

            <TouchableOpacity style={[styles.largeButton, {backgroundColor: '#02AF14'}]} onPress={onViewBeneficiaries}>
                <Text style={[styles.buttonText, {fontSize: vs(11)}]}>VER BENEFICIARIOS</Text>
                <Ionicons name="people-outline" size={vs(18)} color="#fff"/>
            </TouchableOpacity>


            <TouchableOpacity style={[styles.largeButton, {backgroundColor: '#0077cc'}]} onPress={onViewCredential}>
                <Text style={[styles.buttonText, {fontSize: vs(11)}]}>VER CREDENCIAL DIGITAL</Text>
                <Ionicons name="card-outline" size={vs(18)} color="#fff"/>
            </TouchableOpacity>

        </>
    );
}
