// components/common/QrImage.tsx

import React from 'react';
import { View, StyleSheet } from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import {Ionicons} from "@expo/vector-icons";
import {vs} from "react-native-size-matters";

type Props = {
    loading: boolean;
    error: string;
    link: string;
    size?: number;
    backgroundColor?: string;
    padding?: number;
};

export default function QrImage({   loading,
                                    error,
                                    link,
                                    size = 90,
                                    backgroundColor = '#FFF',
                                    padding = 10,
                                }: Props) {
    return (
        <View style={[styles.container, { backgroundColor, padding }]}>
            {link && !error ? (
                <QRCode value={link} size={size} />
            ) :
                <Ionicons name={"cloud-offline-outline"} size={vs(50)} />
            }
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        borderRadius: 12,
        alignSelf: 'center',
    },
});
