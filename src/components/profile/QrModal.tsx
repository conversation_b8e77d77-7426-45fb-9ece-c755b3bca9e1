import {Modal, Pressable, TouchableWithoutFeedback, View} from "react-native";
import {BlurView} from "expo-blur";
import {vs} from "react-native-size-matters";
import {Ionicons} from "@expo/vector-icons";
import QrImage from "./QrImage";
import React, {useEffect, useState} from "react";
import useCredentialQr from "../../hooks/useCredentialQr";
import {useAuth} from "../../context/AuthContext";
import * as Brightness from 'expo-brightness';


interface Props {
    loadingQr: boolean;
    errorQr: string;
    link: string;
    modalQr: boolean;
    setModalQr: (visible: boolean) => void;
    previousBrightness: number;
}


const QrModal = ({loadingQr, errorQr, link,modalQr, setModalQr, previousBrightness}: Props) => {

    const handleCloseModal = () => {
        setModalQr(false);

        if (previousBrightness !== null) {
            Brightness.setSystemBrightnessAsync(previousBrightness).catch(console.warn);
        }
    }


    return (
        <Modal visible={modalQr} transparent animationType={"slide"}>
            <TouchableWithoutFeedback onPress={() => handleCloseModal()}>
                <View style={{ flex: 1 }}>
                    <BlurView intensity={70} tint="dark" style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                        <TouchableWithoutFeedback>
                            <View style={{ backgroundColor: 'rgba(218,214,214,1)', padding: vs(20), borderRadius: vs(25), alignItems: "flex-end" }}>
                                <QrImage
                                    loading={loadingQr}
                                    error={errorQr}
                                    link={link}
                                    padding={vs(5)}
                                    size={vs(210)}
                                />
                            </View>
                        </TouchableWithoutFeedback>
                    </BlurView>
                </View>
            </TouchableWithoutFeedback>
        </Modal>
    )
}

export default QrModal