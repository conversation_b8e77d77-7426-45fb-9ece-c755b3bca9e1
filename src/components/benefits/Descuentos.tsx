import {Image, Text, TouchableOpacity, View} from "react-native";
import {homeStyles as styles} from "../../styles/homeStyle";
import React from "react";
import {useNavigation} from "@react-navigation/native";
import {NativeStackNavigationProp} from "@react-navigation/native-stack";
import {RootStackParamList} from "../../navigation/AppNavigator";



interface Benefit {
    id: string;
    title: string;
    description: string;
    validity_start_date: string;
    validity_end_date: string;
    image: string;
}

export default function Descuentos({descuentos}){
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList, 'BenefitDetails'>>();

/*    console.log('🎁 Descuentos - Renderizando componente');
    console.log('📊 Número de descuentos recibidos:', descuentos?.length || 0);*/

/*    if (descuentos && descuentos.length > 0) {
        console.log('📋 Lista de descuentos:');
        descuentos.forEach((descuento, index) => {
            console.log(`  ${index + 1}. ${descuento.title} (ID: ${descuento.id})`);
            console.log(`     Imagen: ${descuento.image}`);
        });
    } else {
        console.warn('⚠️ No hay descuentos para mostrar');
    }*/

    const handleModal = (item: Benefit) => {
/*        console.log('🔍 Abriendo detalle de beneficio:', item.title);*/
        navigation.navigate('BenefitDetails', {descuento: item})
    }

    return(
        <View style={styles.gridContainer}>
            {descuentos && descuentos.length > 0 ? (
                descuentos.map((item, idx) => {
/*                    console.log(`🖼️ Renderizando beneficio ${idx}:`, {
                        id: item.id,
                        title: item.title,
                        image: item.image,
                        hasImage: !!item.image
                    });*/

                    return (
                        <View key={idx} style={styles.gridItem}>
                            <TouchableOpacity onPress={() => handleModal(item)}>
                                <Image
                                    source={{uri: item.image}}
                                    style={styles.cardImage}
                                    resizeMode="cover"
/*                                    onLoad={() => console.log('✅ Imagen de beneficio cargada:', item.title)}
                                    onError={(error) => console.error('❌ Error al cargar imagen de beneficio:', item.title, error.nativeEvent.error)}
                                    onLoadStart={() => console.log('⏳ Iniciando carga imagen beneficio:', item.title)}
                                    onLoadEnd={() => console.log('🏁 Finalizó carga imagen beneficio:', item.title)}*/
                                />
                            </TouchableOpacity>
                            <View style={styles.cardOverlay}>
                                <Text style={styles.cardText}>{item.title}</Text>
                            </View>
                        </View>
                    );
                })
            ) : (
                <View style={{padding: 20, alignItems: 'center'}}>
                    <Text style={{color: '#666', fontSize: 16}}>No hay beneficios disponibles</Text>
                </View>
            )}
        </View>
    )
}