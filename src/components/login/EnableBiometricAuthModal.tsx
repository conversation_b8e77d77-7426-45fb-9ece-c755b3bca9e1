import {
    Modal,
    Pressable,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View
} from "react-native";
import {BlurView} from "expo-blur";
import {vs} from "react-native-size-matters";
import QrImage from "../profile/QrImage";
import React, {useCallback, useState} from "react";
import {Ionicons} from "@expo/vector-icons";
import {ValidationErrors} from "../../hooks/useValidation";
import {borderRadius, globalTheme} from "../../constants/theme";
import {useFocusEffect} from "@react-navigation/native";
import changeNavigationBarColor from "react-native-navigation-bar-color";
import {error} from "react-native-gifted-chat/lib/logging";

interface Props {
    ebaModal: boolean;
    handleCloseModal: any;
    handleEnableAuth: (emailModal: string, passwordModal: string) => any;
    modalErrors: ValidationErrors;
}

const EnableBiometricAuthModal = ({
                                    ebaModal,
                                    handleCloseModal,
                                    handleEnableAuth,
                                    modalErrors
                                  }: Props) => {

    const [emailModal, setEmail] = useState(null);
    const [passwordModal, setPassword] = useState(null);
    const [showPassword, setShowPassword] = useState(false);
    const [error, setError] = useState<boolean>(false);

    const validateInputs = () => {

        if (!emailModal && !passwordModal) {
            setError(true);
        } else {
            handleEnableAuth(emailModal, passwordModal)
            cleanInputs()
        }
    }

    const onClose = () => {
        handleCloseModal(false);
        cleanInputs()
    }

    const cleanInputs = () => {
        setError(false);
        setEmail('');
        setPassword('');
    }


    return (
        <Modal visible={ebaModal} transparent animationType={"fade"}>
            <TouchableWithoutFeedback onPress={() => onClose()}>
                <BlurView intensity={60} tint="dark" style={{ flex: 1, justifyContent: 'flex-end', alignItems: 'center' }}/>
            </TouchableWithoutFeedback>
            <View style={styles.container}>
                <View style={styles.containerHead}>
                    <Text style={styles.title}>BIOMÉTRICOS</Text>
                    <Pressable
                        onPress={() => onClose()}
                    >
                        <Ionicons
                            name={'close'}
                            size={vs(25)}
                        />
                    </Pressable>
                </View>
                <Text style={styles.label}>CORREO O TELÉFONO</Text>
                <View style={[styles.inputContainer, {marginBottom: vs(10)}]}>
                    <TextInput
                        style={styles.input}
                        value={emailModal}
                        onChangeText={(text) => {
                            setEmail(text);
                        }}
                        placeholder="<EMAIL>"
                        placeholderTextColor={globalTheme.text_placeholder}
                        autoCapitalize="none"
                        autoComplete="off"
                        textContentType="none"
                        autoCorrect={false}
                        spellCheck={false}
                    />
                </View>

                <Text style={styles.label}>CONTRASEÑA</Text>
                <View style={[styles.inputContainer, {marginBottom: vs(10)}]}>
                    <TextInput
                        style={[styles.input]}
                        value={passwordModal}
                        onChangeText={(text) => {
                            setPassword(text);
                        }}
                        placeholder="Contraseña"
                        placeholderTextColor={globalTheme.text_placeholder}
                        secureTextEntry={!showPassword}
                        autoComplete="off"
                        textContentType="none"
                        autoCorrect={false}
                        spellCheck={false}
                    />
                    <TouchableOpacity
                        style={styles.iconOverlay}
                        onPress={() => setShowPassword(!showPassword)}
                    >
                        <Ionicons
                            name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                            size={vs(17)}
                            color={globalTheme.container_dark}
                        />
                    </TouchableOpacity>
                </View>


                <Text
                    style={[styles.warn,
                        {color: error ? globalTheme.text_error : globalTheme.text_head}]}
                >
                    Ingresa tus credenciales
                </Text>


                <TouchableOpacity
                    onPress={() => {
                        validateInputs();
                        setEmail('');
                        setPassword('');
                    }}
                    style={styles.loginBtn} >
                    <Text style={styles.loginText}>ACTIVAR</Text>
                </TouchableOpacity>

            </View>
        </Modal>
    )
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: globalTheme.background,
        padding: vs(20),
        paddingBottom: vs(35),
        alignItems: "center",
        width: '100%',
        borderTopStartRadius: borderRadius,
        borderTopEndRadius: borderRadius,
        marginTop: -vs(25),
    },
    containerHead: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: vs(10),
    },
    title: {
        color: globalTheme.text_contrast,
        fontSize: vs(16),
        fontWeight: 'bold',
    },
    warn: {
        fontSize: vs(10),
        fontWeight: 'semibold',
        textAlign: 'center',
        marginBottom: vs(10),
        width: '100%',
    },
    label: {
        color: globalTheme.text_contrast,
        alignContent: 'center',
        marginBottom: vs(5),
        fontSize: vs(14),
        fontWeight: '400',
        width: '100%'
    },
    inputContainer: {
        justifyContent: 'center',
        width: '100%',
    },
    input: {
        backgroundColor: globalTheme.input,
        borderRadius: borderRadius,
        borderWidth: 0.5,
        borderColor: globalTheme.container_medium,
        paddingHorizontal: vs(15),
        paddingVertical: vs(6),
        color: globalTheme.text_contrast,
        width: '100%',
    },
    iconOverlay: {
        position: 'absolute',
        right: vs(12),
        zIndex: 1,
    },
    loginBtn: {
        backgroundColor: globalTheme.button_ok,
        borderRadius: borderRadius,
        paddingVertical: vs(8),
        alignItems: 'center',
        width: vs(145),
    },
    loginText: {
        color: globalTheme.text_head,
        fontWeight: 'bold',
    },
})

export default EnableBiometricAuthModal