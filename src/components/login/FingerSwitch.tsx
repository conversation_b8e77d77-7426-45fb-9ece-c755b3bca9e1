import {StyleSheet, Switch, Text, View} from "react-native";
import {MaterialCommunityIcons} from "@expo/vector-icons";
import React from "react";
import {vs} from "react-native-size-matters";


interface Props {
    isEnabled: boolean;
    handleOpenModal: any;
}

const FingerSwitch = ({
                          isEnabled,
                          handleOpenModal
                      }: Props) => {

    return (
        <View style={styles.container}>
            <MaterialCommunityIcons
                name={isEnabled ? "fingerprint" : "fingerprint-off"}
                size={vs(25)}
                color={"#FFF"}
            />
            <Switch
                trackColor={{false: '#1C4488', true: '#7FC42B'}}
                thumbColor={"#FFF"}
                ios_backgroundColor="#3e3e3e"
                onValueChange={handleOpenModal}
                value={isEnabled}
            />
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        flexDirection: "row",
        gap: 5
    }
})

export default FingerSwitch