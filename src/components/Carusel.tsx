import React from 'react';
import {View, Image, Dimensions, StyleSheet, Linking, Pressable} from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import { vs } from "react-native-size-matters";
import { normalizeImageUrl } from '../utils/imageUtils';

const { width } = Dimensions.get('window');

interface Post {
    id: number;
    title: string;
    description: string;
    image: string;
    url: string;
    platform: string;
    start_date: string;
    end_date: string;
}

interface Props {
    posts: Post[];
}

const CarouselComponent = ({ posts }: Props) => {

    console.log('🎠 Carrusel - Posts recibidos:', posts.length);
    posts.forEach((post, index) => {
        console.log(`📸 Post ${index}:`, {
            id: post.id,
            title: post.title,
            image: post.image,
            imageLength: post.image?.length,
            imageStartsWith: post.image?.substring(0, 50) + '...'
        });
    });

    const handlePressOnPost = async (url: string) => {
        if (url) {
            await Linking.openURL(url);
        }
    }

    return (
        <View style={styles.container}>
            <Carousel
                loop={true}  // Carrusel infinito
                autoPlay={true}  // AutoPlay habilitado
                autoPlayInterval={3000}  // Intervalo de 3 segundos entre cada imagen
                onSnapToItem={(index) => {}} // console.log('📍 Carrusel en índice:', index)
                width={width}  // Establece el ancho del carrusel
                height={vs(100)}  // Establece la altura de las imágenes
                data={posts}
                renderItem={({ item }) => {
                    const normalizedImageUri = normalizeImageUrl(item.image);
                    // console.log('🖼️ Renderizando imagen:', {
                    //     id: item.id,
                    //     originalUri: item.image,
                    //     normalizedUri: normalizedImageUri,
                    //     imageValid: !!normalizedImageUri,
                    //     uriChanged: item.image !== normalizedImageUri
                    // });

                    return (
                        <Pressable
                            onPress={() => handlePressOnPost(item?.url)}
                        >
                            <View style={styles.imageContainer}>
                                <Image
                                    source={{ uri: normalizedImageUri }}
                                    style={styles.image}
/*                                    onLoad={() => console.log('✅ Imagen cargada exitosamente:', item.id)}
                                    onError={(error) => console.error('❌ Error al cargar imagen:', item.id, error.nativeEvent.error)}
                                    onLoadStart={() => console.log('⏳ Iniciando carga de imagen:', item.id)}
                                    onLoadEnd={() => console.log('🏁 Finalizó carga de imagen:', item.id)}*/
                                />
                            </View>
                        </Pressable>
                    );
                }}
                scrollAnimationDuration={1000}  // Duración de la animación de desplazamiento
                // Ajuste para que el espacio entre imágenes sea de 5px
                mode={"parallax"}
                modeConfig={{
                    parallaxScrollingScale: 1,
                    parallaxScrollingOffset: vs(200),
                }}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    image: {
        width: vs(100),  // Ajusta el tamaño de la imagen
        height: vs(100),  // Ajusta el tamaño de la imagen
        borderRadius: 10,
        resizeMode: 'cover',
    },
    carouselContent: {
        marginHorizontal: 5,  // Espacio de 5px entre las imágenes
    },
});

export default CarouselComponent;
