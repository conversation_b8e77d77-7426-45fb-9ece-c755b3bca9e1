import { useState, useCallback } from 'react';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  phone?: boolean;
  custom?: (value: any) => string | null;
}

export interface FieldConfig {
  id: string;
  type: string;
  label: string;
  rules?: ValidationRule;
  [key: string]: any;
}

export interface FormErrors {
  [fieldId: string]: string;
}

export interface FormValues {
  [fieldId: string]: any;
}

export function useFormValidation(fields: FieldConfig[]) {
  const [values, setValues] = useState<FormValues>({});
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<{ [fieldId: string]: boolean }>({});

  // Inicializar valores por defecto
  const initializeValues = useCallback((initialValues: FormValues = {}) => {
    const defaultValues: FormValues = {};
    fields.forEach(field => {
      if (initialValues[field.id] !== undefined) {
        defaultValues[field.id] = initialValues[field.id];
      } else {
        // Valores por defecto según el tipo
        switch (field.type) {
          case 'checkbox':
            defaultValues[field.id] = [];
            break;
          case 'text':
          case 'textarea':
          case 'email':
          case 'phone':
          case 'number':
          default:
            defaultValues[field.id] = '';
            break;
        }
      }
    });
    setValues(defaultValues);
  }, [fields]);

  // Validar un campo individual
  const validateField = useCallback((fieldId: string, value: any): string => {
    const field = fields.find(f => f.id === fieldId);
    if (!field || !field.rules) return '';

    const rules = field.rules;

    // Validación requerido
    if (rules.required) {
      if (value === null || value === undefined || value === '') {
        return `${field.label} es requerido`;
      }
      if (Array.isArray(value) && value.length === 0) {
        return `${field.label} es requerido`;
      }
    }

    // Si el campo está vacío y no es requerido, no validar más
    if (!value || (Array.isArray(value) && value.length === 0)) {
      return '';
    }

    // Validación de longitud mínima
    if (rules.minLength && typeof value === 'string' && value.length < rules.minLength) {
      return `${field.label} debe tener al menos ${rules.minLength} caracteres`;
    }

    // Validación de longitud máxima
    if (rules.maxLength && typeof value === 'string' && value.length > rules.maxLength) {
      return `${field.label} no puede tener más de ${rules.maxLength} caracteres`;
    }

    // Validación de email
    if (rules.email && typeof value === 'string') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return `${field.label} debe ser un email válido`;
      }
    }

    // Validación de teléfono
    if (rules.phone && typeof value === 'string') {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
        return `${field.label} debe ser un número de teléfono válido`;
      }
    }

    // Validación con patrón personalizado
    if (rules.pattern && typeof value === 'string' && !rules.pattern.test(value)) {
      return `${field.label} no tiene el formato correcto`;
    }

    // Validación personalizada
    if (rules.custom) {
      const customError = rules.custom(value);
      if (customError) {
        return customError;
      }
    }

    return '';
  }, [fields]);

  // Actualizar valor de un campo
  const updateValue = useCallback((fieldId: string, value: any) => {
    setValues(prev => ({ ...prev, [fieldId]: value }));
    
    // Validar en tiempo real si el campo ya fue tocado
    if (touched[fieldId]) {
      const error = validateField(fieldId, value);
      setErrors(prev => ({ ...prev, [fieldId]: error }));
    }
  }, [touched, validateField]);

  // Marcar campo como tocado
  const touchField = useCallback((fieldId: string) => {
    setTouched(prev => ({ ...prev, [fieldId]: true }));
    
    // Validar el campo cuando se toca
    const value = values[fieldId];
    const error = validateField(fieldId, value);
    setErrors(prev => ({ ...prev, [fieldId]: error }));
  }, [values, validateField]);

  // Validar todos los campos
  const validateAll = useCallback((): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    fields.forEach(field => {
      const error = validateField(field.id, values[field.id]);
      if (error) {
        newErrors[field.id] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    
    // Marcar todos los campos como tocados
    const allTouched: { [fieldId: string]: boolean } = {};
    fields.forEach(field => {
      allTouched[field.id] = true;
    });
    setTouched(allTouched);

    return isValid;
  }, [fields, values, validateField]);

  // Limpiar errores
  const clearErrors = useCallback(() => {
    setErrors({});
    setTouched({});
  }, []);

  // Resetear formulario
  const resetForm = useCallback((initialValues: FormValues = {}) => {
    initializeValues(initialValues);
    setErrors({});
    setTouched({});
  }, [initializeValues]);

  // Obtener si el formulario es válido
  const isValid = Object.keys(errors).length === 0 || Object.values(errors).every(error => !error);

  // Obtener si hay campos tocados con errores
  const hasErrors = Object.entries(errors).some(([fieldId, error]) => touched[fieldId] && error);

  return {
    values,
    errors,
    touched,
    isValid,
    hasErrors,
    updateValue,
    touchField,
    validateAll,
    validateField,
    clearErrors,
    resetForm,
    initializeValues,
  };
}

// Reglas de validación comunes
export const ValidationRules = {
  required: { required: true },
  email: { required: true, email: true },
  phone: { required: true, phone: true },
  minLength: (length: number) => ({ minLength: length }),
  maxLength: (length: number) => ({ maxLength: length }),
  pattern: (regex: RegExp) => ({ pattern: regex }),
  custom: (validator: (value: any) => string | null) => ({ custom: validator }),

  // Combinaciones comunes
  requiredText: (minLength = 1) => ({ required: true, minLength }),
  requiredEmail: { required: true, email: true },
  requiredPhone: { required: true, phone: true },
  optionalEmail: { email: true },
  optionalPhone: { phone: true },

  // Validaciones específicas para formularios
  shortText: { required: true, minLength: 2, maxLength: 100 },
  longText: { required: true, minLength: 10, maxLength: 1000 },
  optionalShortText: { maxLength: 100 },
  optionalLongText: { maxLength: 1000 },

  // Validaciones para números
  positiveNumber: {
    required: true,
    custom: (value: any) => {
      const num = parseFloat(value);
      if (isNaN(num) || num <= 0) {
        return 'Debe ser un número positivo';
      }
      return null;
    }
  },

  // Validación para CURP mexicano
  curp: {
    required: true,
    pattern: /^[A-Z]{4}[0-9]{6}[HM][A-Z]{5}[0-9]{2}$/,
    custom: (value: string) => {
      if (!value) return null;
      if (value.length !== 18) {
        return 'El CURP debe tener 18 caracteres';
      }
      return null;
    }
  },

  // Validación para RFC mexicano
  rfc: {
    required: true,
    pattern: /^[A-ZÑ&]{3,4}[0-9]{6}[A-Z0-9]{3}$/,
    custom: (value: string) => {
      if (!value) return null;
      if (value.length < 12 || value.length > 13) {
        return 'El RFC debe tener 12 o 13 caracteres';
      }
      return null;
    }
  },
};
