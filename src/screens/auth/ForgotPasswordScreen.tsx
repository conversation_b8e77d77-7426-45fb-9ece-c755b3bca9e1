import React, {useEffect, useState} from 'react';
import {
    View,
    TextInput,
    TouchableOpacity,
    KeyboardAvoidingView,
    Platform,
    StyleSheet, Text, ScrollView, ActivityIndicator
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {borderRadius, globalTheme} from "../../constants/theme";
import { loginStyles as styles } from '../../styles/loginStyles';
import { initiatePasswordResetByEmail } from '../../services/emailVerificationService';
import { initiatePasswordResetByPhone } from '../../services/phoneVerificationService';
import { showSuccessAlert, showErrorAlert } from '../../utils/alertUtils';
import LogoTS from "../../components/svg/LogoTS";
import {vs} from "react-native-size-matters";
import {SafeAreaView} from 'react-native-safe-area-context';
import changeNavigationBarColor from "react-native-navigation-bar-color";

export default function ForgotPasswordScreen({ navigation }: any) {
    const [method, setMethod] = useState('email'); // 'email' o 'phone'
    const [email, setEmail] = useState('');
    const [phone, setPhone] = useState('');
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        changeNavigationBarColor(globalTheme.gradient[1], true);
    }, []);

    // Función auxiliar para obtener el mensaje de error amigable
    const getErrorMessage = (response: any, defaultMsg: string) => {
        if (response && typeof response === 'object') {
            if (typeof response.message === 'string' && response.message.trim()) {
                return response.message;
            }
        }
        return defaultMsg;
    };

    // Función auxiliar para obtener el error_code del backend
    const getErrorCode = (response: any) => {
        if (response && typeof response === 'object') {
            // Buscar error_code directamente en la respuesta (formato del backend)
            if (typeof response.error_code === 'string') {
                return response.error_code;
            }
            // Buscar en response.error.code (formato alternativo)
            if (response.error && typeof response.error.code === 'string') {
                return response.error.code;
            }
            // Buscar en response.code (formato del servicio)
            if (typeof response.code === 'string') {
                return response.code;
            }
        }
        return undefined;
    };

    const handleRecover = async () => {
        if (method === 'email') {
            if (!email.trim() || !email.includes('@')) {
                showErrorAlert({
                    error: {
                        code: 'VALIDATION_INVALID_EMAIL',
                        message: 'Por favor ingresa un correo válido.'
                    }
                });
                return;
            }

            setLoading(true);

            try {
                const response = await initiatePasswordResetByEmail(email.trim());

                if (!response.success) {
                    setLoading(false);
                    const errorCode = getErrorCode(response) || 'GENERIC';
                    showErrorAlert({
                        error: {
                            code: errorCode,
                            message: getErrorMessage(response, 'No se pudo enviar el código.')
                        }
                    });
                } else {
                    console.log("[ForgotPassword] Respuesta exitosa:", response);

                    if (!response.user_id) {
                        setLoading(false);
                        showErrorAlert({
                            error: {
                                code: 'SERVER_ERROR_500',
                                message: 'Error: No se recibió el ID de usuario'
                            }
                        });
                        return;
                    }

                    showSuccessAlert(
                        '✅ Código enviado',
                        'Hemos enviado un código de verificación a tu correo.',
                        () => {
                            console.log("[ForgotPassword] Navegando a ValidateCode con email, userId:", response.user_id);
                            // Mantener loading durante la navegación
                            navigation.navigate('ValidateCode', {
                                method: 'email',
                                email: email.trim(),
                                userId: response.user_id,
                                isPasswordReset: true
                            });
                            // Quitar loading después de navegar
                            setLoading(false);
                        }
                    );
                }
            } catch (error: any) {
                console.error('❌ Error en handleRecover:', error);
                // Manejo robusto de errores de Axios o genéricos
                let msg = 'Ocurrió un problema inesperado. Intenta de nuevo.';
                let code = undefined;
                if (error && error.response && error.response.data) {
                    // Error del backend con formato esperado
                    msg = getErrorMessage(error.response.data, msg);
                    code = getErrorCode(error.response.data);
                } else if (error && typeof error === 'object' && error.message) {
                    // Error de red o Axios sin response.data
                    msg = error.message;
                }

                showErrorAlert({
                    error: {
                        code: code || 'NETWORK_ERROR',
                        message: msg
                    }
                });
                setLoading(false);
            }
        } else {
            if (!phone.trim()) {
                showErrorAlert({
                    error: {
                        code: 'VALIDATION_INVALID_PHONE',
                        message: 'Por favor ingresa un número de teléfono válido.'
                    }
                });
                return;
            }
            
            console.log("[ForgotPassword] Iniciando recuperación por teléfono:", phone.trim());
            setLoading(true);

            try {
                const response = await initiatePasswordResetByPhone(phone.trim());
                console.log("[ForgotPassword] Respuesta completa del servicio:", response);

                if (!response.success) {
                    console.error("[ForgotPassword] Error en la respuesta:", response);
                    const errorCode = getErrorCode(response) || 'GENERIC';
                    showErrorAlert({
                        error: {
                            code: errorCode,
                            message: getErrorMessage(response, 'No se pudo enviar el código SMS.')
                        }
                    });
                    setLoading(false);
                } else {
                    console.log("[ForgotPassword] Respuesta exitosa:", response.data);

                    if (!response.data || !response.data.userId) {
                        console.error("[ForgotPassword] Error: No se recibió userId:", response);
                        showErrorAlert({
                            error: {
                                code: 'SERVER_ERROR_500',
                                message: 'Error: No se recibió el ID de usuario del servidor'
                            }
                        });
                        setLoading(false);
                        return;
                    }

                    console.log("[ForgotPassword] SMS enviado exitosamente, navegando a ValidateCode");
                    showSuccessAlert(
                        '✅ Código SMS enviado',
                        `Se ha enviado un código de verificación al número ${phone.trim()}.`,
                        () => {
                            console.log("[ForgotPassword] Navegando a ValidateCode con phone, userId:", response.data.userId);
                            // Mantener loading durante la navegación
                            navigation.navigate('ValidateCode', {
                                method: 'phone',
                                phone: phone.trim(),
                                userId: response.data.userId,
                                isPasswordReset: true
                            });
                            // Quitar loading después de navegar
                            setLoading(false);
                        }
                    );
                }
            } catch (error: any) {
                console.error('❌ [ForgotPassword] Excepción capturada:', error);
                // Manejo robusto de errores de Axios o genéricos
                let msg = 'Ocurrió un problema inesperado al enviar el SMS. Intenta de nuevo.';
                let code = undefined;
                if (error && error.response && error.response.data) {
                    // Error del backend con formato esperado
                    msg = getErrorMessage(error.response.data, msg);
                    code = getErrorCode(error.response.data);
                } else if (error && typeof error === 'object' && error.message) {
                    // Error de red o Axios sin response.data
                    msg = error.message;
                }

                showErrorAlert({
                    error: {
                        code: code || 'NETWORK_ERROR',
                        message: msg
                    }
                });
                setLoading(false);
            }
        }
    };

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: globalTheme.gradient[1] }} edges={['top']}>
            <View style={StyleSheet.absoluteFill}>
                <LinearGradient
                    colors={[globalTheme.gradient[0], globalTheme.gradient[1]]}
                    style={StyleSheet.absoluteFillObject}
                />
            </View>

            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                style={{flex: 1}}
            >
                <View style={styles.wrapper}>
                    <ScrollView
                        contentContainerStyle={styles.scrollContainer}
                        keyboardShouldPersistTaps="handled"
                    >
                        <LogoTS size={"sm"}/>

                        <View style={styles.form}>
                            <Text style={styles.title}>RECUPERAR CONTRASEÑA</Text>
                            
                            <View style={localStyles.methodSelector}>
                                <TouchableOpacity 
                                    style={[
                                        localStyles.methodButton,
                                        method === 'email' && localStyles.methodButtonActive
                                    ]}
                                    onPress={() => setMethod('email')}
                                >
                                    <Text style={[
                                        localStyles.methodText,
                                        method === 'email' && localStyles.methodTextActive
                                    ]}>Correo</Text>
                                </TouchableOpacity>
                                <TouchableOpacity 
                                    style={[
                                        localStyles.methodButton,
                                        method === 'phone' && localStyles.methodButtonActive
                                    ]}
                                    onPress={() => setMethod('phone')}
                                >
                                    <Text style={[
                                        localStyles.methodText,
                                        method === 'phone' && localStyles.methodTextActive
                                    ]}>Teléfono</Text>
                                </TouchableOpacity>
                            </View>

                            {method === 'email' ? (
                                <>
                                    <Text style={styles.label}>INGRESA TU CORREO</Text>
                                    <View style={styles.inputContainer}>
                                        <TextInput
                                            style={styles.input}
                                            placeholder="<EMAIL>"
                                            placeholderTextColor={globalTheme.text_placeholder}
                                            keyboardType="email-address"
                                            autoCapitalize="none"
                                            value={email}
                                            onChangeText={setEmail}
                                        />
                                    </View>
                                </>
                            ) : (
                                <>
                                    <Text style={styles.label}>INGRESA TU TELÉFONO</Text>
                                    <View style={styles.inputContainer}>
                                        <TextInput
                                            style={styles.input}
                                            placeholder="Número de teléfono"
                                            placeholderTextColor={globalTheme.text_placeholder}
                                            keyboardType="phone-pad"
                                            value={phone}
                                            onChangeText={setPhone}
                                        />
                                    </View>
                                </>
                            )}

                            <TouchableOpacity
                                style={styles.loginBtn}
                                onPress={handleRecover}
                                disabled={loading}
                            >
                                <Text style={styles.buttonText}>
                                    {loading ? "ENVIANDO" : "CONTINUAR"}
                                </Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={{ bottom: vs(15), position: "absolute", alignSelf: "center" }}
                                onPress={() => navigation.navigate('AvisoPrivacidad', {aceptable: false})}
                            >
                                <Text style={styles.link1}>Aviso de privacidad</Text>
                            </TouchableOpacity>
                        </View>
                    </ScrollView>
                </View>
            </KeyboardAvoidingView>

            {/* Loading Overlay */}
            {loading && (
                <BlurView intensity={20} tint="dark" style={localStyles.loadingOverlay}>
                    <View style={localStyles.loadingCard}>
                        <ActivityIndicator size="large" color={globalTheme.gradient[0]} />
                        <Text style={localStyles.loadingText}>Enviando código...</Text>
                    </View>
                </BlurView>
            )}
        </SafeAreaView>
    );
}

const localStyles = StyleSheet.create({
    methodSelector: {
        flexDirection: 'row',
        marginBottom: vs(25),
        borderRadius: borderRadius,
        overflow: 'hidden',
    },
    methodButton: {
        flex: 1,
        paddingVertical: vs(8),
        backgroundColor: globalTheme.container_translucent,
        alignItems: 'center',
    },
    methodButtonActive: {
        backgroundColor: globalTheme.gradient[1],
    },
    methodText: {
        color: globalTheme.text_head,
        fontWeight: '600',
    },
    methodTextActive: {
        color:globalTheme.text_head,
    },
    loadingOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    loadingCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: vs(25),
        borderRadius: 15,
        alignItems: 'center',
        minWidth: vs(140),
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    loadingText: {
        marginTop: vs(12),
        color: '#000000',
        fontSize: vs(15),
        fontWeight: '600',
        textAlign: 'center',
    },
});
