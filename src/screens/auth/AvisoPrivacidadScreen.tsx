import BaseScreen from "../../components/BaseScreen";
import {<PERSON><PERSON><PERSON>ler, ScrollView, StyleSheet, Text, TouchableOpacity, View} from "react-native";
import {Ionicons} from "@expo/vector-icons";
import {mvs, vs} from "react-native-size-matters";
import React, {useEffect, useState} from "react";
import LogoTS from '../../components/svg/LogoTS'
import {RouteProp, useRoute} from "@react-navigation/native";
import {RootStackParamList} from "../../navigation/AppNavigator";
import {globalTheme} from "../../constants/theme";

export default function AvisoPrivacidadScreen({navigation}: any) {



    const route = useRoute<RouteProp<RootStackParamList, 'AvisoPrivacidadScreen'>>();
    const {aceptable} = route.params;
    const {form} = route.params;
    const [aviso, setAviso] = useState<boolean>(route.params?.aceptado || false);

    const previousScreen = route.params?.previousScreen || null;


    // Función para manejar el regreso personalizado
    const handleGoBack = (aceptado: boolean) => {

        setAviso(aceptado)

        if (previousScreen === 'register') {
            navigation.navigate('Register', {aceptado: aceptado, form: form});
        } else {
            // Regreso estándar
            navigation.goBack();
        }
    };

    useEffect(() => {
        const backAction = () => {
            navigation.reset({
                index: 0,
                routes: [{ name: 'Register' }],
            });
            return true; // Prevenir el comportamiento predeterminado de retroceso
        };

        // Agregar el listener de retroceso
        const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

        return () => backHandler.remove();
    }, [navigation]);

    return (
        <BaseScreen
            scroll={false}
            navColor={globalTheme.gradient[1]}
        >
            <View style={styles.nav}>
                <TouchableOpacity style={styles.button} onPress={() => handleGoBack(aviso)}>
                    <Ionicons name="reload" size={mvs(25, 0.5)} color="#fff"/>
                    <Text style={styles.buttonText}>Regresar</Text>
                </TouchableOpacity>
            </View>
            <View style={{alignSelf: "center"}}>
                <LogoTS size={"sm"}/>
            </View>
            <View style={styles.container}>
                <View style={{height: vs(40), justifyContent: "center"}}>
                    <Text style={styles.title}>AVISO DE PRIVACIDAD</Text>
                </View>
                <View style={styles.content}>
                    <ScrollView>
                        <Text style={styles.text}>
                            AVISO DE PRIVACIDAD{"\n\n"}

                            GRUPO TORRES, con domicilio en [Dirección completa], es el responsable del tratamiento de los datos personales que nos proporcione, los cuales serán protegidos conforme a lo dispuesto por la Ley Federal de Protección de Datos Personales en Posesión de Particulares, y demás normatividad que resulte aplicable.{"\n\n"}

                            FINALIDADES DEL TRATAMIENTO{"\n"}
                            Los datos personales que recabamos de usted, los utilizaremos para las siguientes finalidades que son necesarias para el servicio que solicita:{"\n"}
                            • Verificar y confirmar su identidad{"\n"}
                            • Gestionar el acceso a la aplicación móvil{"\n"}
                            • Proporcionar los servicios solicitados{"\n"}
                            • Comunicar información relevante sobre su empleo{"\n"}
                            • Gestionar beneficios y prestaciones laborales{"\n"}
                            • Atender sus consultas, solicitudes y reclamaciones{"\n\n"}

                            DATOS PERSONALES TRATADOS{"\n"}
                            Para llevar a cabo las finalidades descritas en el presente aviso de privacidad, utilizaremos los siguientes datos personales:{"\n"}
                            • Datos de identificación: Nombre, CURP, número de empleado{"\n"}
                            • Datos de contacto: Teléfono, correo electrónico{"\n"}
                            • Datos laborales: Puesto, área, antigüedad{"\n\n"}

                            TRANSFERENCIA DE DATOS{"\n"}
                            Le informamos que sus datos personales no serán transferidos ni tratados por terceros, salvo aquellas excepciones previstas por la Ley y para los casos específicamente señalados en este aviso de privacidad.{"\n\n"}

                            DERECHOS ARCO{"\n"}
                            Usted tiene derecho a conocer qué datos personales tenemos de usted, para qué los utilizamos y las condiciones del uso que les damos (Acceso). Asimismo, es su derecho solicitar la corrección de su información personal en caso de que esté desactualizada, sea inexacta o incompleta (Rectificación); que la eliminemos de nuestros registros o bases de datos cuando considere que la misma no está siendo utilizada adecuadamente (Cancelación); así como oponerse al uso de sus datos personales para fines específicos (Oposición). Estos derechos se conocen como derechos ARCO.{"\n\n"}

                            Para el ejercicio de cualquiera de los derechos ARCO, usted deberá presentar la solicitud respectiva a través del correo electrónico: [correo de contacto].{"\n\n"}

                            CAMBIOS AL AVISO DE PRIVACIDAD{"\n"}
                            El presente aviso de privacidad puede sufrir modificaciones, cambios o actualizaciones derivadas de nuevos requerimientos legales; de nuestras propias necesidades por los productos o servicios que ofrecemos; de nuestras prácticas de privacidad; de cambios en nuestro modelo de negocio, o por otras causas. Nos comprometemos a mantenerlo informado sobre los cambios que pueda sufrir el presente aviso de privacidad, a través de la aplicación móvil.
                        </Text>
                    </ScrollView>
                </View>
                <TouchableOpacity
                    onPress={() => {
                        handleGoBack(!aviso);
                    }}
                    style={{flexDirection: 'row', height: vs(50), alignItems: "center"}}
                >
                    {aceptable && (
                        <>
                            <Ionicons name={aviso ? 'checkbox' : 'square-outline' } color={'white'} size={vs(14)}/>
                            <Text style={styles.agreement}>ESTOY DE ACUERDO</Text>
                        </>
                    )}
                </TouchableOpacity>
            </View>
        </BaseScreen>
    )
}

const styles = StyleSheet.create({
        nav: {
            marginTop: vs(35),
            marginLeft: vs(20),
        },
        button: {
            flexDirection: "row",
            alignItems: "center"
        },
        buttonText: {
            fontSize: vs(14),
            paddingLeft: vs(5),
            color: globalTheme.text_head
        },
        container: {
            width: '80%',
            height: vs(500),
            alignSelf: "center",
            backgroundColor: globalTheme.container_translucent,
            marginTop: vs(35),
            borderRadius: vs(25),
            alignItems: "center",
        },
        title: {
            fontSize: vs(18),
            color: globalTheme.text_head,
        },
        content: {
            width: '85%',
            flex: 1,
            backgroundColor: globalTheme.container_light,
            borderRadius: vs(12),
            padding: vs(20)
        },
        text: {
            fontSize: vs(9),
            textAlign: "justify",
        },
        agreement: {
            color: globalTheme.text_contrast,
            marginLeft: vs(5)
        }
    }
);
