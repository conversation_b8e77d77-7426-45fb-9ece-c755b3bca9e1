import React, { useState, useEffect, useCallback } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    ActivityIndicator,
    TextInput,
    FlatList,
    RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRoute } from '@react-navigation/native';
import BaseScreen from '../../components/BaseScreen';
import Header from '../../components/common/Header';
import { errorLogsStyles as styles } from '../../styles/errorLogsStyles';
import { getLogFiles, getLogFileContent, getLatestErrors } from '../../services/errorLogsService';
import { showErrorAlert } from '../../utils/alertUtils';
import { vs } from 'react-native-size-matters';

// Log level types
const LOG_LEVELS = ['error', 'warning', 'notice', 'info', 'debug'];

export default function ErrorLogsScreen({ navigation }: any) {
    const route = useRoute();
    const { dominio } = route.params || { dominio: '' };

    // State variables
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [logFiles, setLogFiles] = useState<any[]>([]);
    const [selectedFile, setSelectedFile] = useState<string | null>(null);
    const [logContent, setLogContent] = useState<any>({ lines: [] });
    const [selectedLevel, setSelectedLevel] = useState<string | null>(null);
    const [searchText, setSearchText] = useState('');
    const [latestErrors, setLatestErrors] = useState<any[]>([]);
    const [offset, setOffset] = useState(0);
    const [limit] = useState(100);

    // Fetch log files
    const fetchLogFiles = useCallback(async () => {
        try {
            const response = await getLogFiles(dominio);
            if (response.error) {
                showErrorAlert({
                    error: {
                        code: 'SERVER_ERROR_500',
                        message: response.msg || 'Error al obtener archivos de log'
                    }
                });
            } else {
                setLogFiles(response.data || []);
                if (response.data && response.data.length > 0 && !selectedFile) {
                    setSelectedFile(response.data[0].path);
                }
            }
        } catch (error: any) {
            console.error('Error fetching log files:', error);
            showErrorAlert({
                error: {
                    code: 'NETWORK_ERROR',
                    message: 'Error inesperado al obtener archivos de log'
                }
            });
        }
    }, [dominio, selectedFile]);

    // Fetch log content
    const fetchLogContent = useCallback(async () => {
        if (!selectedFile) return;

        try {
            setLoading(true);
            const response = await getLogFileContent(
                dominio,
                selectedFile,
                selectedLevel || undefined,
                searchText || undefined,
                limit,
                offset
            );

            if (response.error) {
                showErrorAlert({
                    error: {
                        code: 'SERVER_ERROR_500',
                        message: response.msg || 'Error al obtener contenido del log'
                    }
                });
            } else {
                setLogContent(response.data);
            }
        } catch (error: any) {
            console.error('Error fetching log content:', error);
            showErrorAlert({
                error: {
                    code: 'NETWORK_ERROR',
                    message: 'Error inesperado al obtener contenido del log'
                }
            });
        } finally {
            setLoading(false);
        }
    }, [dominio, selectedFile, selectedLevel, searchText, limit, offset]);

    // Fetch latest errors
    const fetchLatestErrors = useCallback(async () => {
        try {
            const response = await getLatestErrors(dominio);
            if (response.error) {
                console.error('Error fetching latest errors:', response.msg);
            } else {
                setLatestErrors(response.data || []);
            }
        } catch (error: any) {
            console.error('Error fetching latest errors:', error);
        }
    }, [dominio]);

    // Initial data loading
    useEffect(() => {
        if (!dominio) {
            showErrorAlert({
                error: {
                    code: 'VALIDATION_MISSING_FIELDS',
                    message: 'Dominio no especificado'
                }
            });
            return;
        }

        const loadData = async () => {
            setLoading(true);
            await fetchLogFiles();
            await fetchLatestErrors();
            setLoading(false);
        };

        loadData();

        // Set up interval for auto-refresh of latest errors
        const intervalId = setInterval(() => {
            fetchLatestErrors();
        }, 30000); // 30 seconds

        return () => clearInterval(intervalId);
    }, [dominio, fetchLogFiles, fetchLatestErrors]);

    // Fetch log content when selected file, level, search, or pagination changes
    useEffect(() => {
        if (selectedFile) {
            fetchLogContent();
        }
    }, [selectedFile, selectedLevel, searchText, offset, fetchLogContent]);

    // Handle refresh
    const onRefresh = async () => {
        setRefreshing(true);
        await fetchLogFiles();
        if (selectedFile) {
            await fetchLogContent();
        }
        await fetchLatestErrors();
        setRefreshing(false);
    };

    // Handle file selection
    const handleFileSelect = (filePath: string) => {
        setSelectedFile(filePath);
        setOffset(0); // Reset pagination when changing files
    };

    // Handle level filter
    const handleLevelFilter = (level: string) => {
        setSelectedLevel(selectedLevel === level ? null : level);
        setOffset(0); // Reset pagination when changing filters
    };

    // Handle search
    const handleSearch = () => {
        setOffset(0); // Reset pagination when searching
        fetchLogContent();
    };

    // Handle pagination
    const handlePrevPage = () => {
        if (offset - limit >= 0) {
            setOffset(offset - limit);
        }
    };

    const handleNextPage = () => {
        if (logContent.total_lines > offset + limit) {
            setOffset(offset + limit);
        }
    };

    // Determine log line style based on content
    const getLogLineStyle = (line: string) => {
        if (line.includes('ERROR') || line.includes('CRITICAL') || line.includes('ALERT') || line.includes('EMERGENCY')) {
            return [styles.logLine, styles.logLineError];
        } else if (line.includes('WARNING')) {
            return [styles.logLine, styles.logLineWarning];
        } else if (line.includes('INFO')) {
            return [styles.logLine, styles.logLineInfo];
        } else if (line.includes('DEBUG')) {
            return [styles.logLine, styles.logLineDebug];
        } else if (line.includes('NOTICE')) {
            return [styles.logLine, styles.logLineNotice];
        }
        return styles.logLine;
    };

    return (
        <BaseScreen>
            <Header />
            <ScrollView
                style={styles.container}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={['#385927']}
                    />
                }
            >
                <Text style={styles.header}>LOGS DE ERRORES</Text>

                {/* Files List */}
                <Text style={styles.latestErrorsHeader}>Archivos de Log</Text>
                {loading && !logFiles.length ? (
                    <ActivityIndicator size="large" color="#385927" />
                ) : (
                    <FlatList
                        data={logFiles}
                        style={styles.filesList}
                        keyExtractor={(item) => item.path}
                        renderItem={({ item }) => (
                            <TouchableOpacity
                                style={[
                                    styles.fileItem,
                                    selectedFile === item.path && styles.fileItemSelected
                                ]}
                                onPress={() => handleFileSelect(item.path)}
                            >
                                <Text style={styles.fileItemText}>{item.name}</Text>
                                <Text style={styles.fileItemInfo}>
                                    {(item.size / 1024).toFixed(2)} KB
                                </Text>
                            </TouchableOpacity>
                        )}
                        ListEmptyComponent={
                            <Text style={styles.emptyText}>No hay archivos de log disponibles</Text>
                        }
                    />
                )}

                {/* Log Content */}
                {selectedFile && (
                    <>
                        <Text style={styles.latestErrorsHeader}>
                            Contenido: {selectedFile}
                        </Text>

                        {/* Filters */}
                        <View style={styles.filterContainer}>
                            {LOG_LEVELS.map((level) => (
                                <TouchableOpacity
                                    key={level}
                                    style={[
                                        styles.filterButton,
                                        selectedLevel === level && styles.filterButtonActive
                                    ]}
                                    onPress={() => handleLevelFilter(level)}
                                >
                                    <Text style={styles.filterButtonText}>
                                        {level.toUpperCase()}
                                    </Text>
                                </TouchableOpacity>
                            ))}
                        </View>

                        {/* Search */}
                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Buscar en el log..."
                                placeholderTextColor="#999"
                                value={searchText}
                                onChangeText={setSearchText}
                                onSubmitEditing={handleSearch}
                            />
                            <TouchableOpacity
                                style={styles.searchButton}
                                onPress={handleSearch}
                            >
                                <Text style={styles.searchButtonText}>Buscar</Text>
                            </TouchableOpacity>
                        </View>

                        {/* Content */}
                        {loading ? (
                            <ActivityIndicator size="large" color="#385927" />
                        ) : (
                            <View style={styles.contentContainer}>
                                <ScrollView style={styles.logContent}>
                                    {logContent.lines && logContent.lines.length > 0 ? (
                                        logContent.lines.map((line: string, index: number) => (
                                            <Text key={index} style={getLogLineStyle(line)}>
                                                {line}
                                            </Text>
                                        ))
                                    ) : (
                                        <Text style={styles.emptyText}>
                                            No hay contenido para mostrar
                                        </Text>
                                    )}
                                </ScrollView>
                            </View>
                        )}

                        {/* Pagination */}
                        {logContent.total_lines > 0 && (
                            <View style={styles.paginationContainer}>
                                <TouchableOpacity
                                    style={[
                                        styles.paginationButton,
                                        offset === 0 && styles.paginationButtonDisabled
                                    ]}
                                    onPress={handlePrevPage}
                                    disabled={offset === 0}
                                >
                                    <Text style={styles.paginationButtonText}>Anterior</Text>
                                </TouchableOpacity>

                                <Text style={styles.paginationInfo}>
                                    {offset + 1}-{Math.min(offset + limit, logContent.total_lines)} de{' '}
                                    {logContent.total_lines}
                                </Text>

                                <TouchableOpacity
                                    style={[
                                        styles.paginationButton,
                                        offset + limit >= logContent.total_lines &&
                                            styles.paginationButtonDisabled
                                    ]}
                                    onPress={handleNextPage}
                                    disabled={offset + limit >= logContent.total_lines}
                                >
                                    <Text style={styles.paginationButtonText}>Siguiente</Text>
                                </TouchableOpacity>
                            </View>
                        )}
                    </>
                )}

                {/* Latest Errors */}
                <View style={styles.latestErrorsContainer}>
                    <Text style={styles.latestErrorsHeader}>Últimos Errores</Text>
                    {latestErrors.length > 0 ? (
                        latestErrors.map((error, index) => (
                            <View key={index} style={styles.latestErrorItem}>
                                <Text style={styles.latestErrorFile}>{error.file}</Text>
                                <Text style={styles.latestErrorContent}>{error.content}</Text>
                            </View>
                        ))
                    ) : (
                        <Text style={styles.emptyText}>No hay errores recientes</Text>
                    )}
                </View>

                {/* Back Button */}
                <TouchableOpacity
                    style={styles.regresar}
                    onPress={() => navigation.goBack()}
                >
                    <Ionicons name="arrow-back" size={24} color="#fff" />
                    <Text style={styles.regresarText}>Regresar</Text>
                </TouchableOpacity>
            </ScrollView>
        </BaseScreen>
    );
}