// ChatScreen.tsx
import {Alert, Pressable, StyleSheet, Text, View} from "react-native";
import {Ionicons, MaterialCommunityIcons} from "@expo/vector-icons";
import {globalTheme} from '../../constants/theme';
import {vs} from "react-native-size-matters";
import {useFocusEffect, useNavigation} from "@react-navigation/native";
import {NativeStackNavigationProp} from "@react-navigation/native-stack";
import {RootStackParamList} from "../../navigation/AppNavigator";
import {GiftedChat, Bubble, InputToolbar, Send, Day, Avatar} from "react-native-gifted-chat";
import React, {useEffect, useState} from "react";
import {LinearGradient} from "expo-linear-gradient";
import {SafeAreaView} from "react-native-safe-area-context";
import {useAuth} from "../../context/AuthContext";
import {publishMessage, useChatCredentials} from "../../hooks/useChat";
import EventSource, {EventSourceListener} from "react-native-sse";
import { DateTime } from 'luxon';
import {ApiPublishMessage} from "../../types/ChatTypes";
import changeNavigationBarColor from "react-native-navigation-bar-color";
import RetryChatConnection from "../../components/RetryChatConnection";

export default function ChatScreen(){
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList, 'ChatScreen'>>();
    const { authState } = useAuth();
    const [messages, setMessages] = useState([]);
    const [hasConnectionError, setHasConnectionError] = useState(false);
    const [retrying, setRetrying] = useState(false);


    useEffect(() => {
        changeNavigationBarColor(globalTheme.gradient[1], true);
    }, []);

    const {
        conversation,
        topic,
        authorization,
    } = useChatCredentials((initialMessages) => {
        setMessages(
            initialMessages.map(m => ({
                _id: m.id,
                text: m.content,
                createdAt: DateTime.fromFormat(m.createdAt, "yyyy-MM-dd HH:mm:ss", { zone: 'UTC' })
                    .setZone('America/Mexico_City')
                    .toJSDate(),

                user: {
                    _id: m.author.id,
                    name: `${m.author.name} ${m.author.lastName}`,
                }
            }))
        );
    });

    useFocusEffect(() => {
        if (!topic || !authorization) return;

        const es = new EventSource(topic, {
            headers: { Authorization: `Bearer ${authorization}` },
        });

        const listener: EventSourceListener = (event) => {
            if (event.type === "message") {
                console.log('mensjae', event.data)
                const data = JSON.parse(event.data);
                const newMessage = {
                    _id: data.id,
                    text: data.content,
                    createdAt: DateTime.fromFormat(data.createdAt, "dd/MM/yyyy HH:mm", { zone: 'America/Mexico_City' })
                        .setZone('America/Mexico_City')
                        .toJSDate(),
                    user: {
                        _id: data.author.id,
                        name: `${data.author.name} ${data.author.lastName}`,
                    }
                };
                setMessages(previousMessages => GiftedChat.append(previousMessages, [newMessage]));
            } else if (event.type === "error") {
                console.error("SSE Error:", event.message);
                setHasConnectionError(true);
            }
        };

        es.addEventListener("message", listener);
        es.addEventListener("error", listener);

        return () => {
            es.removeAllEventListeners();
            es.close();
            setHasConnectionError(false);
        };
    });

    const onSend = async (conversationId: number, content: string) => {

        const data: ApiPublishMessage = await publishMessage(
            conversationId,
            authState.user.id,
            authState.token,
            content
        )

        if (data.code !== 200) {
            Alert.alert('Error al enviar mensaje', 'prueba más tarde')
        }

    };

    const retry = () => {
        setHasConnectionError(false);
        setRetrying((prevRetrying) => !prevRetrying);
    };



    return(
        <SafeAreaView style={{flex: 1, backgroundColor: globalTheme.gradient[0]}} edges={['top']}>
            <View style={StyleSheet.absoluteFill}>
                <LinearGradient
                    colors={[globalTheme.gradient[0], globalTheme.gradient[1]]}
                    style={StyleSheet.absoluteFillObject}
                />
            </View>
            <View style={styles.header}>
                <Pressable onPress={() => navigation.goBack()} style={styles.headerArrow}>
                    <Ionicons name={'arrow-back-outline'} size={vs(25)} color={'#FFF'} />
                </Pressable>
                <Text style={styles.headerText}>ASISTENTE VIRTUAL</Text>
                {hasConnectionError && (
                    <RetryChatConnection retry={retry}/>
                )}
            </View>

            <GiftedChat
                messages={messages}
                onSend={messages => onSend(Number(conversation),messages[0].text)}
                user={{ _id: authState.user.id }}
                placeholder="Escribe tu pregunta..."
                renderBubble={renderCustomBubble}
                renderInputToolbar={renderCustomInput}
                renderSend={renderCustomSend}
                renderDay={renderCustomDay}
                renderAvatar={renderCustomAvatar}
                showAvatarForEveryMessage={true}
                alwaysShowSend={true}
                messagesContainerStyle={{paddingHorizontal: vs(8)}}
                keyboardShouldPersistTaps={'never'}
                renderAvatarOnTop={false}
                loadEarlier={false}
            />
        </SafeAreaView>
    )
}

const renderCustomBubble = (props) => (
    <Bubble
        {...props}
        wrapperStyle={{
            right: {
                backgroundColor: globalTheme.background,
                paddingHorizontal: vs(8),
                borderRadius: vs(25),
                marginBottom: vs(6),
            },
            left: {
                backgroundColor: globalTheme.container_translucent,
                paddingHorizontal: vs(8),
                borderRadius: vs(25),
                marginBottom: vs(6),
            },
        }}
        textStyle={{
            right: { color: globalTheme.text_contrast },
            left: { color: globalTheme.text_head },
        }}
        timeTextStyle={{
            right: { color: globalTheme.text_contrast },
            left: { color: globalTheme.text_head },
        }}

    />
);

const renderCustomInput = (props) => (
    <InputToolbar
        {...props}
        containerStyle={{
            borderTopWidth: 0,
            backgroundColor: 'transparent',
            marginVertical: vs(5),
        }}
        primaryStyle={{
            backgroundColor: globalTheme.background,
            borderRadius: vs(25),
            height: vs(60),
            padding: 0,
            alignItems: "center",
            justifyContent: 'center',
            marginHorizontal: vs(12),
        }}
    />
);

const renderCustomSend = (props) => (
    <Send {...props}>
        <MaterialCommunityIcons name={'send-circle'} style={styles.customSend}/>
    </Send>
);

const renderCustomDay = (props) => (
    <Day
        {...props}
        textStyle={{ color: globalTheme.text_head, fontWeight: 'bold' }}
        wrapperStyle={{ backgroundColor: globalTheme.container_translucent }}
    />
);

const renderCustomAvatar = (props) => {
    return <Avatar
        {...props}
        showAvatarForEveryMessage={false}
        renderAvatarOnTop={true}
    />;
};

const styles = StyleSheet.create({
    header: {
        backgroundColor: globalTheme.container_translucent,
        height: vs(60),
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        marginHorizontal: vs(12),
        marginTop: vs(12),
        borderRadius: vs(25)
    },
    headerArrow: {
        position: "absolute",
        left: vs(15),
    },
    headerText: {
        color: "#FFF",
        fontSize: vs(16),
        fontWeight: '600'
    },
    customSend: {
        color: globalTheme.gradient[1],
        fontSize: vs(35),
        marginRight: vs(5),
    }
});
