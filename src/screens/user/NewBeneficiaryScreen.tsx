import React, {useState, useEffect} from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    ActivityIndicator,
    StyleSheet,
    KeyboardAvoidingView, Platform, ScrollView
} from 'react-native';
import { BlurView } from 'expo-blur';
import {useAuth} from '../../context/AuthContext';
import {createBeneficiario, updateBeneficiario} from '../../services/beneficiarioService';
import {showErrorAlert, showSuccessAlert, showValidationAlert} from '../../utils/alertUtils';
import {Ionicons} from '@expo/vector-icons';
import GenderSelectorModal from '../../components/beneficiarios/GenderSelectorModal';
import KinshipSelectorModal from '../../components/beneficiarios/KinshipSelectorModal';
import EducationSelectorModal from '../../components/beneficiarios/EducationSelectorModal';
import DatePickerModal from '../../components/common/DatePickerModal';
import {vs} from "react-native-size-matters";
import {SafeAreaView} from "react-native-safe-area-context";
import {LinearGradient} from "expo-linear-gradient";
import {globalTheme} from '../../constants/theme';
import UserAvatar from "../../components/common/UserAvatar";
import PhotoPickerModal from "../../components/common/PhotoPickerModal";

// const { width, height } = Dimensions.get('window'); // No usado actualmente
import changeNavigationBarColor from "react-native-navigation-bar-color";

export default function NuevoBeneficiarioScreen({navigation, route}: any) {
    const {authState} = useAuth();
    const [loading, setLoading] = useState(false);
    const [showGenderModal, setShowGenderModal] = useState(false);
    const [showKinshipModal, setShowKinshipModal] = useState(false);
    const [showEducationModal, setShowEducationModal] = useState(false);
    const [showDateModal, setShowDateModal] = useState(false);
    const [showPicker, setShowPicker] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});

    const beneficiario = route.params?.beneficiario;

    const [photoUri, setPhotoUri] = useState(beneficiario?.photo);

    const [form, setForm] = useState({
        name: '',
        last_name: '',
        maternal_last_name: '',
        kinship: '',
        birthday: '',
        gender: '',
        education: '',
        curp: '',
        photo: '',
    });

    useEffect(() => {
        changeNavigationBarColor(globalTheme.gradient[1], true);
    }, []);

    useEffect(() => {
        if (beneficiario) {
            console.log('🔄 Inicializando formulario con beneficiario existente:', beneficiario);
            console.log('📸 Foto del beneficiario:', beneficiario.photo);

            setForm({
                name: beneficiario.name || '',
                last_name: beneficiario.last_name || '',
                maternal_last_name: beneficiario.maternal_last_name || '',
                kinship: beneficiario.kinship || '',
                birthday: beneficiario.birthday?.split(' ')[0] || '',
                gender: beneficiario.gender || '',
                education: beneficiario.education || '',
                curp: beneficiario.curp || '',
                photo: beneficiario.photo || '',
            });

            // Sincronizar photoUri con la foto del beneficiario
            if (beneficiario.photo) {
                setPhotoUri(beneficiario.photo);
                console.log('📸 PhotoUri sincronizado:', beneficiario.photo);
            }
        }
    }, [beneficiario]);

    const handleChange = (key: string, value: string) => {
        const processedValue = key === 'curp' ? value.toUpperCase() : value;
        setForm((prev) => ({...prev, [key]: processedValue}));

        // Limpiar error del campo cuando el usuario empiece a escribir
        if (errors[key]) {
            setErrors((prev) => ({...prev, [key]: ''}));
        }
    };

    // Validación específica para CURP en tiempo real (igual que en registro)
    const validateCURPRealTime = (curp: string) => {
        if (!curp) {
            return 'El CURP es obligatorio.';
        }

        if (typeof curp !== 'string') {
            return 'El CURP debe ser una cadena de texto.';
        }

        if (curp.length !== 18) {
            return 'La CURP debe tener exactamente 18 caracteres';
        }

        // Validación de formato CURP
        const curpRegex = /^[A-Z]{4}[0-9]{6}[HM][A-Z]{5}[0-9A-Z][0-9]$/;
        if (!curpRegex.test(curp.toUpperCase())) {
            return 'El formato del CURP no es válido.';
        }

        return null; // Sin errores
    };

    const handleCURPChange = (value: string) => {
        // Filtrar solo números y letras, sin símbolos (igual que en registro)
        const filteredValue = value.replace(/[^A-Za-z0-9]/g, '');
        const upperValue = filteredValue.toUpperCase();
        setForm({ ...form, curp: upperValue });

        // Validar solo si tiene contenido
        if (upperValue.trim()) {
            const error = validateCURPRealTime(upperValue);
            if (error) {
                setErrors({ ...errors, curp: error });
            } else {
                setErrors({ ...errors, curp: '' });
            }
        } else {
            setErrors({ ...errors, curp: '' });
        }
    };


    const validateForm = () => {
        console.log('🔍 validateForm - Iniciando validación');
        console.log('📝 Formulario actual:', form);

        const newErrors: Record<string, string> = {};

        // Validación nombre (siguiendo las validaciones del registro)
        if (!form.name.trim()) {
            newErrors.name = 'El nombre es obligatorio.';
        } else if (form.name.trim().length < 2) {
            newErrors.name = 'El nombre debe tener al menos 2 caracteres.';
        }

        // Validación apellido paterno
        if (!form.last_name.trim()) {
            newErrors.last_name = 'El apellido paterno es obligatorio.';
        } else if (form.last_name.trim().length < 2) {
            newErrors.last_name = 'El apellido debe tener al menos 2 caracteres.';
        }

        // Validación apellido materno (opcional pero si se llena debe ser válido)
        if (form.maternal_last_name && form.maternal_last_name.trim().length < 2) {
            newErrors.maternal_last_name = 'El apellido materno debe tener al menos 2 caracteres.';
        }

        // Validación parentesco
        if (!form.kinship.trim()) {
            newErrors.kinship = 'El parentesco es obligatorio.';
        }

        // Validación fecha de nacimiento
        if (!form.birthday.trim()) {
            newErrors.birthday = 'La fecha de nacimiento es obligatoria.';
        } else {
            const fechaRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!fechaRegex.test(form.birthday)) {
                newErrors.birthday = 'La fecha debe tener el formato YYYY-MM-DD.';
            }
        }

        // Validación género
        if (!form.gender.trim()) {
            newErrors.gender = 'El género es obligatorio.';
        }

        // Validación educación
        if (!form.education.trim()) {
            newErrors.education = 'El nivel educativo es obligatorio.';
        }

        // Validación CURP (igual que en registro)
        if (!form.curp.trim()) {
            newErrors.curp = 'El CURP es obligatorio.';
        } else if (typeof form.curp !== 'string') {
            newErrors.curp = 'El CURP debe ser una cadena de texto.';
        } else if (form.curp.trim().length !== 18) {
            newErrors.curp = 'La CURP debe tener exactamente 18 caracteres';
        } else if (!/^[A-Z]{4}[0-9]{6}[HM][A-Z]{5}[0-9A-Z][0-9]$/.test(form.curp.trim().toUpperCase())) {
            newErrors.curp = 'El formato del CURP no es válido.';
        }

        setErrors(newErrors);
        console.log('🔍 Errores de validación:', newErrors);

        // Si hay errores, mostrar el primero
        if (Object.keys(newErrors).length > 0) {
            const firstError = Object.values(newErrors)[0];
            showValidationAlert('form', firstError);
            console.log('❌ Validación fallida:', firstError);
            return false;
        }

        console.log('✅ Validación exitosa');
        return true;
    };

    const handleSave = async () => {
        console.log('🚀 handleSave - Iniciando proceso de guardado');
        console.log('👤 authState.user:', authState.user);

        if (!validateForm()) {
            console.log('❌ Validación fallida, abortando guardado');
            return;
        }

        console.log('⏳ Iniciando loading...');
        setLoading(true);

        const userId = authState.user?.user_id;
        console.log('🆔 userId extraído:', userId);

        if (!userId) {
            console.error('❌ No se encontró userId');
            showErrorAlert({error: {code: 'NO_USER_ID', message: 'Usuario no identificado'}});
            setLoading(false);
            return;
        }

        const payload = {...form};
        console.log('📦 Payload construido:', payload);
        console.log('📸 Foto en payload:', payload.photo ? 'SÍ' : 'NO');
        if (payload.photo) {
            console.log('📸 Tipo de foto:', payload.photo.startsWith('file://') ? 'Archivo local' : 'URL/Base64');
        }

        console.log('📤 Enviando payload beneficiario:', payload);

        let result: any;
        if (beneficiario?.id) {
            console.log('🔄 Actualizando beneficiario existente:', beneficiario.id);
            console.log('📋 Datos del beneficiario existente:', beneficiario);
            result = await updateBeneficiario(userId, beneficiario.id, payload);
        } else {
            console.log('➕ Creando nuevo beneficiario');
            result = await createBeneficiario(userId, payload);
        }

        console.log('📥 Resultado beneficiario:', result);
        console.log('🔍 Tipo de resultado:', typeof result);
        console.log('❓ ¿Es error?:', result.error);
        console.log('💬 Mensaje:', result.msg);

        if (result.error) {
            console.error('❌ Error al guardar beneficiario:', result);
            console.error('📝 Mensaje de error específico:', result.msg);
            showErrorAlert({error: {code: 'SAVE_ERROR', message: result.msg}});
        } else {
            console.log('✅ Beneficiario guardado exitosamente');
            console.log('📋 Datos del beneficiario guardado:', result);

            // Actualizar el estado local con la foto que devuelve el backend
            if (result.beneficiary && result.beneficiary.photo) {
                console.log('📸 Actualizando foto local con respuesta del backend:', result.beneficiary.photo);
                setPhotoUri(result.beneficiary.photo);
                setForm(prev => ({ ...prev, photo: result.beneficiary.photo }));
            }

            showSuccessAlert('✅ Beneficiario guardado', 'Se ha guardado correctamente.');
            console.log('🔙 Navegando hacia atrás...');
            navigation.goBack();
        }

        console.log('⏳ Finalizando loading...');
        setLoading(false);
    };

    const handlePickPhoto = (uri: string) => {
        console.log('📸 Foto seleccionada en NewBeneficiaryScreen:', uri);
        console.log('🔍 Detalles de la URI:');
        console.log('  - Tipo:', typeof uri);
        console.log('  - Longitud:', uri.length);
        console.log('  - Empieza con file://:', uri.startsWith('file://'));

        setPhotoUri(uri); // Actualiza el estado de la imagen seleccionada
        setForm((prev) => {
            const newFormData = { ...prev, photo: uri };
            console.log('📝 FormData actualizado en beneficiario:', newFormData);
            return newFormData;
        });
        console.log('✅ Estados actualizados - photoUri y form.photo');
    };


    return (
        <SafeAreaView style={{flex: 1}} edges={['top']}>

            <View style={StyleSheet.absoluteFill}>
                <LinearGradient
                    colors={[globalTheme.gradient[0], globalTheme.gradient[1]]}
                    style={StyleSheet.absoluteFillObject}
                />
            </View>


            <KeyboardAvoidingView
                style={{flex: 1}}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}

            >
                <View style={styles.wrapper}>
                    <ScrollView
                        contentContainerStyle={styles.scrollContainer}
                        keyboardShouldPersistTaps="handled"
                    >

                        <View style={styles.container}>
                            <View style={styles.bodyImage}>
                                <UserAvatar
                                    uri={photoUri}
                                    onPress={() => setShowPicker(true)}
                                    isEditing={true}
                                />
                            </View>

                            <Text style={styles.label}>NOMBRE *</Text>
                            <TextInput
                                placeholder="Nombre del beneficiario"
                                value={form.name}
                                onChangeText={(text) => handleChange('name', text)}
                                style={[styles.input, errors.name && styles.inputError]}
                            />
                            {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}

                            <Text style={styles.label}>APELLIDO PATERNO *</Text>
                            <TextInput
                                placeholder="Apellido paterno"
                                value={form.last_name}
                                onChangeText={(text) => handleChange('last_name', text)}
                                style={[styles.input, errors.last_name && styles.inputError]}
                            />
                            {errors.last_name && <Text style={styles.errorText}>{errors.last_name}</Text>}

                            <Text style={styles.label}>APELLIDO MATERNO</Text>
                            <TextInput
                                placeholder="Apellido materno (opcional)"
                                value={form.maternal_last_name}
                                onChangeText={(text) => handleChange('maternal_last_name', text)}
                                style={[styles.input, errors.maternal_last_name && styles.inputError]}
                            />
                            {errors.maternal_last_name && <Text style={styles.errorText}>{errors.maternal_last_name}</Text>}

                            <Text style={styles.label}>PARENTESCO *</Text>
                            <TouchableOpacity
                                style={[styles.input, styles.selectInput, errors.kinship && styles.inputError]}
                                onPress={() => setShowKinshipModal(true)}
                            >
                                <Text style={[styles.selectText, !form.kinship && styles.placeholderText]}>
                                    {form.kinship || 'SELECCIONAR PARENTESCO'}
                                </Text>
                                <Ionicons name="chevron-down" size={20} color="#666" />
                            </TouchableOpacity>
                            {errors.kinship && <Text style={styles.errorText}>{errors.kinship}</Text>}
                            <Text style={styles.label}>FECHA DE NACIMIENTO *</Text>
                            <TouchableOpacity
                                style={[styles.input, styles.selectInput, errors.birthday && styles.inputError]}
                                onPress={() => setShowDateModal(true)}
                            >
                                <Text style={[styles.selectText, !form.birthday && styles.placeholderText]}>
                                    {form.birthday || 'SELECCIONAR FECHA DE NACIMIENTO'}
                                </Text>
                                <Ionicons name="calendar-outline" size={20} color="#666" />
                            </TouchableOpacity>
                            {errors.birthday && <Text style={styles.errorText}>{errors.birthday}</Text>}

                            <Text style={styles.label}>GÉNERO *</Text>
                            <TouchableOpacity
                                style={[styles.input, styles.selectInput, errors.gender && styles.inputError]}
                                onPress={() => setShowGenderModal(true)}
                            >
                                <Text style={[styles.selectText, !form.gender && styles.placeholderText]}>
                                    {form.gender || 'SELECCIONAR GÉNERO'}
                                </Text>
                                <Ionicons name="chevron-down" size={20} color="#666" />
                            </TouchableOpacity>
                            {errors.gender && <Text style={styles.errorText}>{errors.gender}</Text>}

                            <Text style={styles.label}>NIVEL EDUCATIVO *</Text>
                            <TouchableOpacity
                                style={[styles.input, styles.selectInput, errors.education && styles.inputError]}
                                onPress={() => setShowEducationModal(true)}
                            >
                                <Text style={[styles.selectText, !form.education && styles.placeholderText]}>
                                    {form.education || 'SELECCIONAR NIVEL EDUCATIVO'}
                                </Text>
                                <Ionicons name="chevron-down" size={20} color="#666" />
                            </TouchableOpacity>
                            {errors.education && <Text style={styles.errorText}>{errors.education}</Text>}

                            <Text style={styles.label}>CURP *</Text>
                            <TextInput
                                placeholder="CURP (18 caracteres)"
                                value={form.curp}
                                onChangeText={handleCURPChange}
                                style={[styles.input, errors.curp && styles.inputError]}
                                autoCapitalize="characters"
                                maxLength={18}
                            />
                            {errors.curp && <Text style={styles.errorText}>{errors.curp}</Text>}


                            <TouchableOpacity style={[styles.saveButton, loading && styles.saveButtonDisabled]} onPress={handleSave} disabled={loading}>
                                <Text style={styles.saveButtonText}>
                                    {beneficiario?.id ? 'ACTUALIZAR BENEFICIARIO' : 'GUARDAR BENEFICIARIO'}
                                </Text>
                            </TouchableOpacity>
                        </View>

                        <TouchableOpacity style={styles.regresar} onPress={() => navigation.goBack()}>
                            <Ionicons name={'reload'} color={"#FFF"} size={vs(20)}/>
                            <Text style={styles.regresarText}>Regresar</Text>
                        </TouchableOpacity>

                        {/* Modales de selección */}
                        <GenderSelectorModal
                            visible={showGenderModal}
                            onSelect={(selectedGender) => {
                                handleChange('gender', selectedGender);
                                setShowGenderModal(false);
                            }}
                            onClose={() => setShowGenderModal(false)}
                        />

                        <KinshipSelectorModal
                            visible={showKinshipModal}
                            onSelect={(kinship) => {
                                handleChange('kinship', kinship);
                                setShowKinshipModal(false);
                            }}
                            onClose={() => setShowKinshipModal(false)}
                        />

                        <EducationSelectorModal
                            visible={showEducationModal}
                            onSelect={(education) => {
                                handleChange('education', education);
                                setShowEducationModal(false);
                            }}
                            onClose={() => setShowEducationModal(false)}
                        />

                        <DatePickerModal
                            visible={showDateModal}
                            onSelect={(selectedDate) => {
                                handleChange('birthday', selectedDate);
                                setShowDateModal(false);
                            }}
                            onClose={() => setShowDateModal(false)}
                        />

                        <PhotoPickerModal
                            visible={showPicker}
                            onClose={() => setShowPicker(false)}
                            onPick={handlePickPhoto}
                            isForBeneficiary={true}
                        />


                    </ScrollView>
                </View>
            </KeyboardAvoidingView>

            {/* Loading Overlay */}
            {loading && (
                <BlurView intensity={20} tint="dark" style={loadingOverlayStyles.loadingOverlay}>
                    <View style={loadingOverlayStyles.loadingCard}>
                        <ActivityIndicator size="large" color="#1698BF" />
                        <Text style={loadingOverlayStyles.loadingText}>
                            {beneficiario?.id ? 'Actualizando beneficiario...' : 'Guardando beneficiario...'}
                        </Text>
                    </View>
                </BlurView>
            )}
        </SafeAreaView>

    );
}

const styles = StyleSheet.create({
    scrollContainer: {
        flexGrow: 1,
        alignItems: 'center',
    },
    wrapper: {
        flex: 1,
        position: 'relative',
    },
    container: {
        backgroundColor: 'rgba(218,214,214,0.34)',
        borderRadius: vs(25),
        padding: vs(15),
        paddingTop: vs(70),
        paddingBottom: vs(30),
        alignSelf: "center",
        marginVertical: vs(20),
        marginTop: vs(80),
        width: '85%',
    },
    bodyImage: {
        zIndex: 3,
        position:"absolute",
        alignSelf: "center",
        marginTop: -vs(40)
    },
    label: {
        fontSize: vs(12),
        fontWeight: 'bold',
        color: globalTheme.text_contrast,
        marginBottom: vs(5),
        marginTop: vs(10),
    },
    input: {
        backgroundColor: 'white',
        borderRadius: vs(25),
        paddingHorizontal: vs(15),
        paddingVertical: vs(6),
        marginBottom: vs(15),
        fontSize: vs(14),
        color: '#333',
        borderWidth: 1,
        borderColor: 'transparent',
    },
    inputError: {
        borderColor: globalTheme.text_error,
        borderWidth: 2,
    },
    selectInput: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    selectText: {
        fontSize: vs(14),
        color: '#333',
    },
    placeholderText: {
        color: '#999',
    },
    errorText: {
        color: globalTheme.text_error,
        fontSize: vs(11),
        marginTop: vs(-10),
        marginBottom: vs(10),
        marginLeft: vs(15),
    },
    saveButton: {
        backgroundColor: '#008E0E',
        borderRadius: 30,
        paddingVertical: vs(8),
        alignItems: 'center',
        width: vs(200),
        alignSelf: "center"
    },
    saveButtonDisabled: {
        backgroundColor: '#ccc',
        opacity: 0.7,
    },
    saveButtonText: {
        color: 'white',
            fontWeight: 'bold',
            fontSize: 16,
    },
    regresar: {
        flexDirection: 'row',
            alignItems: 'center',
            alignSelf: 'center',
            gap: 8,
            alignContent: 'center',
    },
    regresarText: {
        color: 'white',
        textDecorationLine: 'underline',
        fontSize: vs(14),
    },
});

const loadingOverlayStyles = {
    loadingOverlay: {
        position: 'absolute' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
        zIndex: 1000,
    },
    loadingCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 25,
        borderRadius: 15,
        alignItems: 'center' as const,
        minWidth: 140,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    loadingText: {
        marginTop: 12,
        color: '#000000',
        fontSize: 15,
        fontWeight: '600' as const,
        textAlign: 'center' as const,
    },
};