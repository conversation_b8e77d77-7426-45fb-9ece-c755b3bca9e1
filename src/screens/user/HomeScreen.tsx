import React, { useRef, useEffect, useState } from 'react';
import { Text, View, ScrollView, Image, Animated, ActivityIndicator, Dimensions } from 'react-native';
import BaseScreen from '../../components/BaseScreen';
import Header from '../../components/common/Header';
import { homeStyles as styles } from '../../styles/homeStyle';
import * as Animatable from 'react-native-animatable';
import Carusel from "../../components/Carusel";
import { vs } from "react-native-size-matters";
import Descuentos from "../../components/benefits/Descuentos";
import { useBenefits } from "../../hooks/useBenefits";
import { useAuth } from "../../context/AuthContext";
import { usePost } from "../../hooks/usePost";
import { formatDateForBanner } from "../../hooks/formatDateForBanner";
import ErrorComponent from "../../components/common/ErrorComponents";
import { useNotification } from "../../context/NotificationContext";
import useSaveExpoToken from "../../hooks/useSaveExpoToken";
import ChatIcon from "../../components/ChatIcon";
import {globalTheme} from "../../constants/theme";
import { analyzePostImages, normalizeImageUrl } from "../../utils/imageUtils";

interface Post {
    id: number;
    title: string;
    description: string;
    image: string;
    url: string;
    platform: string;
    start_date: string;
    end_date: string;
}

interface Benefit {
    id: string;
    title: string;
    description: string;
    validity_start_date: string;
    validity_end_date: string;
    image: string;
}

export default function HomeScreen({ navigation }: any) {
    const { width } = Dimensions.get('window');
    const scrollRef = useRef<ScrollView>(null);
    const scaleAnim = useRef(new Animated.Value(1)).current;
    const { authState } = useAuth();
    const [benefits, setBenefits] = useState<Benefit[]>([]);
    const [post, setPost] = useState<Post[]>([]);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    const formatToYMD = (date: Date) => {
        return date.toISOString().split('T')[0];
    };

    const start_date = formatToYMD(firstDayOfMonth);
    const end_date = formatToYMD(lastDayOfMonth);

    const { notification, expoPushToken } = useNotification();
    const { loadingSaveToken, saveToken } = useSaveExpoToken();

    useEffect(() => {
        const syncToken = () => {
/*            console.log('🔄 Sincronizando token de dispositivo');
            console.log('📱 expoPushToken:', expoPushToken);
            console.log('🔑 authState.token:', authState.token ? 'PRESENTE' : 'FALTANTE');
            console.log('👤 authState.user.user_id:', authState.user?.user_id);*/

            // Validar que tenemos todos los datos necesarios
            if (!expoPushToken || !authState.token || !authState.user?.user_id) {
                console.warn('⚠️ No se puede sincronizar token - faltan datos:', {
                    expoPushToken: expoPushToken ? 'PRESENTE' : 'FALTANTE',
                    token: authState.token ? 'PRESENTE' : 'FALTANTE',
                    userId: authState.user?.user_id ? 'PRESENTE' : 'FALTANTE'
                });
                return;
            }

            saveToken(expoPushToken, authState.token, String(authState.user.user_id));
        };

        if (authState?.pastUser !== authState?.user?.user_id ||
            authState?.pastDeviceToken !== expoPushToken) {
            syncToken()
        }

    }, [expoPushToken, authState.token, authState.user?.user_id, authState.pastUser, authState.pastDeviceToken]);

    const { loadingPost, errorPost } = usePost({
        company_id: authState?.user?.company_id,
        token: authState?.token,
        start_date,
        end_date,
        amount: 5,
        setPost
    });

    const { loadingBenefits, errorBenefits } = useBenefits({
        companyId: authState?.user?.company_id,
        page: 1,
        token: authState?.token,
        setBenefits,
    });

/*    console.log('🏠 HomeScreen - Estado de beneficios:', {
        loadingBenefits,
        errorBenefits,
        benefitsCount: benefits.length,
        companyId: authState?.user?.company_id
    });*/

/*
    if (benefits.length > 0) {
        console.log('🎁 HomeScreen - Beneficios disponibles:');
        benefits.forEach((benefit, index) => {
            console.log(`  ${index + 1}. ${benefit.title} (ID: ${benefit.id})`);
        });
    } else {
        console.log('⚠️ HomeScreen - No hay beneficios en el estado');
    }
*/

    useEffect(() => {
        let scrollX = 0;
        let scrollInterval: NodeJS.Timeout | null = null;

        // Solo iniciar el scroll automático si hay posts y el componente está montado
        if (post && post.length > 1) {
            scrollInterval = setInterval(() => {
                scrollX += 300;
                // Verificar que el ref y el componente aún existen
                if (scrollRef.current) {
                    try {
                        scrollRef.current.scrollTo({ x: scrollX, animated: true });
                    } catch (error) {
                        // Limpiar interval si hay error
                        if (scrollInterval) {
                            clearInterval(scrollInterval);
                            scrollInterval = null;
                        }
                    }
                }
            }, 4000);
        }

        return () => {
            if (scrollInterval) {
                clearInterval(scrollInterval);
                scrollInterval = null;
            }
        };
    }, [post]);

    useEffect(() => {
        let animationRef: any = null;

        animationRef = Animated.loop(
            Animated.sequence([
                Animated.timing(scaleAnim, {
                    toValue: 1.03,
                    duration: 2000,
                    useNativeDriver: true,
                }),
                Animated.timing(scaleAnim, {
                    toValue: 1,
                    duration: 2000,
                    useNativeDriver: true,
                }),
            ])
        );

        animationRef.start();

        return () => {
            if (animationRef) {
                animationRef.stop();
                animationRef = null;
            }
        };
    }, [scaleAnim]);

    if (errorBenefits && errorPost) {
        return (
            <BaseScreen>
                <Header />
                <View style={{ backgroundColor: "#FFF", height: "100%" }}>
                    <ErrorComponent />
                </View>
            </BaseScreen>
        );
    }

/*    console.log('📋 HomeScreen - Posts totales:', post.length);
    post.forEach((p, index) => {
        console.log(`📝 Post ${index}:`, {
            id: p.id,
            title: p.title,
            image: p.image,
            hasImage: !!p.image,
            imageExtension: p.image ? p.image.split('.').pop() : 'NO IMAGE'
        });
    });*/

    const postsWithImages = post.filter(
        p =>
            p.image &&
            (p.image.endsWith('.jpg') ||
                p.image.endsWith('.jpeg') ||
                p.image.endsWith('.png'))
    );

/*    console.log('🖼️ Posts con imágenes válidas:', postsWithImages.length);*/
    postsWithImages.forEach((p, index) => {
/*        console.log(`✅ Post válido ${index}:`, {
            id: p.id,
            title: p.title,
            image: p.image
        });*/
    });

    // Analizar imágenes cuando hay posts (solo una vez)
    React.useEffect(() => {
        if (post.length > 0 && !loadingPost) {
/*            console.log('🔍 Iniciando análisis de imágenes...');*/
            analyzePostImages(post);
        }
    }, [post, loadingPost]);

    return (
        <BaseScreen
            chat={true}
            navColor={globalTheme.navigation_light}
        >
            <Header />
            <Animatable.View animation="fadeInUp" duration={800} delay={200}>
                <ScrollView style={{ backgroundColor: globalTheme.background, height: '100%', paddingTop: vs(20), gap: vs(10) }}>
                    {!loadingPost && !loadingBenefits ? (
                        <>
                            {(postsWithImages.length > 0) && (
                                <>
                                    <Animatable.View animation="fadeInUp" delay={300}>
                                        <Text style={styles.sectionTitle}>LO ÚLTIMO EN REDES SOCIALES</Text>
                                        <Carusel posts={postsWithImages} />
                                    </Animatable.View>

                                    <Animatable.View animation="fadeInUp" delay={500} style={{ alignSelf: 'center' }}>
                                        <Text style={styles.sectionTitle}>¡LO QUE SE VIENE!</Text>

                                        <Image
                                            source={{ uri: normalizeImageUrl(postsWithImages[0].image) }}
                                            style={{
                                                width: width * 0.9,
                                                height: (width * 0.9) * 9 / 16,
                                                borderRadius: 10,
                                            }}
                                            resizeMode={"cover"}
                                        />

                                        <View style={styles.imageInfo}>
                                            <Text style={styles.infoTitle}>{postsWithImages[0].title}</Text>
                                            <Text style={styles.infoDate}>
                                                {formatDateForBanner(
                                                    postsWithImages[0].start_date,
                                                    postsWithImages[0].end_date
                                                )}
                                            </Text>
                                        </View>
                                    </Animatable.View>
                                </>
                            )}

                            { authState.configuration?.['beneficios'] === '1' && (
                                <Animatable.View animation="fadeInUp" delay={700}>
                                    <Text style={[styles.sectionTitle, { marginBottom: vs(6) }]}>
                                        ¡APROVECHA NUESTROS DESCUENTOS AQUÍ!
                                    </Text>
                                    <Descuentos descuentos={benefits} />
                                </Animatable.View>
                            )}
                        </>
                    ) : (
                        <ActivityIndicator size={"large"} color={'#000'} />
                    )}
                </ScrollView>
            </Animatable.View>
        </BaseScreen>
    );
}
