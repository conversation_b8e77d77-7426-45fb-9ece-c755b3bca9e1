import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import BaseScreen from '../../components/BaseScreen';
import Header from '../../components/common/Header';
import { profileStyle as styles } from '../../styles/profileStyles';
import { getProfile, updateProfile } from '../../services/profileService';
import { useAuth } from '../../context/AuthContext';
import * as SecureStore from 'expo-secure-store';
import ProfileForm from '../../components/profile/ProfileForm';
import ProfileActions from '../../components/profile/ProfileActions';
import {showErrorAlert, showSuccessAlert, showNoChangesAlert} from '../../utils/alertUtils';
import PhotoPickerModal from "../../components/common/PhotoPickerModal";
import UserAvatar from "../../components/common/UserAvatar";
import {globalTheme} from "../../constants/theme";

export default function ProfileScreen({ navigation }: any) {
    const { authState, setAuthState } = useAuth();
    const [isEditing, setIsEditing] = useState(false);
    const [showPicker, setShowPicker] = useState(false);
    const [photoUri, setPhotoUri] = useState<string | null>(authState.user?.photo as string || null);

    const [formData, setFormData] = useState({
        nombre: '',
        apellidoPaterno: '',
        telefono: '',
        correo: '',
        empresa: authState.user?.company_name || 'No disponible',
        photo: (authState.user?.photo as string) || undefined
    });

    // Actualizar photoUri cuando cambie el authState.user.photo
    useEffect(() => {
        if (authState.user?.photo && authState.user.photo !== photoUri) {
            setPhotoUri(authState.user.photo as string);
        }
    }, [authState.user?.photo]);

    useEffect(() => {
        const fetchProfile = async () => {
            const response = await getProfile();

            if (!response.error && response.profile) {
                const profilePhoto = response.profile.photo || (authState.user?.photo as string) || undefined;

                setFormData({
                    nombre: response.profile.name || '',
                    apellidoPaterno: response.profile.last_name || '',
                    telefono: response.profile.phone_number || '',
                    correo: response.profile.email || '',
                    empresa: authState.user?.company_name || 'No disponible',
                    photo: profilePhoto
                });

                // Actualizar también el photoUri si hay una foto
                if (profilePhoto) {
                    setPhotoUri(profilePhoto);
                }
            } else {
                showErrorAlert({
                    error: {
                        code: 'PROFILE_FETCH_ERROR',
                        message: response.msg || 'No se pudo cargar el perfil del usuario.',
                    },
                });
            }
        };

        fetchProfile();
    }, []);

    const handleSaveProfile = async () => {
        console.log('🚀 handleSaveProfile - Iniciando proceso de guardado');
        console.log('👤 authState.user:', authState.user);

        const userId = authState.user?.user_id;
        console.log('🆔 userId extraído:', userId);

        if (!userId) {
            console.error('❌ No se encontró userId');
            showErrorAlert({ error: { code: 'NO_USER_ID', message: 'No se pudo identificar al usuario' } });
            return;
        }

        // Construimos el payload nuevo
        console.log('📝 formData actual:', formData);
        console.log('🔍 Análisis detallado de formData.photo:');
        console.log('  - Valor:', formData.photo);
        console.log('  - Tipo:', typeof formData.photo);
        console.log('  - Es undefined:', formData.photo === undefined);
        console.log('  - Es null:', formData.photo === null);
        console.log('  - Es string vacío:', formData.photo === '');
        console.log('  - Empieza con file://:', formData.photo && formData.photo.startsWith('file://'));

        const payload = {
            name: formData.nombre.trim(),
            last_name: formData.apellidoPaterno.trim(),
            phone_number: formData.telefono.trim(),
            email: formData.correo.trim(),
            employee_number: authState.user?.employee_number,
            company_id: authState.user?.company_id,
            photo: formData.photo,
        };

        console.log('📦 payload construido:', payload);
        console.log('🔍 Análisis detallado de payload.photo:');
        console.log('  - Valor:', payload.photo);
        console.log('  - Tipo:', typeof payload.photo);
        console.log('  - Empieza con file://:', payload.photo && payload.photo.startsWith('file://'));


        console.log('🔍 Comparando cambios:');
        console.log('  - name:', `"${payload.name}" vs "${authState.user?.name}"`);
        console.log('  - last_name:', `"${payload.last_name}" vs "${authState.user?.last_name}"`);
        console.log('  - phone_number:', `"${payload.phone_number}" vs "${authState.user?.phone_number}"`);
        console.log('  - email:', `"${payload.email}" vs "${authState.user?.email}"`);
        console.log('  - photo:', `"${payload.photo}" vs "${authState.user?.photo}"`);

        const hasChanges =
            payload.name !== authState.user?.name ||
            payload.last_name !== authState.user?.last_name ||
            payload.phone_number !== authState.user?.phone_number ||
            payload.email !== authState.user?.email ||
            payload.photo !== authState.user?.photo;

        console.log('🔄 hasChanges:', hasChanges);

        if (!hasChanges) {
            console.log('ℹ️ No se detectaron cambios');
            showNoChangesAlert({ message: 'Sin cambios detectados',description: 'No realizaste ninguna modificación en tu perfil.'});
            setIsEditing(false);
            return;
        }


        console.log('📤 Enviando payload a updateProfile:', payload);
        const result = await updateProfile(userId, payload);
        console.log('📥 Resultado recibido de updateProfile:', result);

        if (result.error) {
            console.error('❌ Error al actualizar perfil:', result);
            console.error('💬 Mensaje de error:', result.msg);
            showErrorAlert({ error: { code: 'UPDATE_ERROR', message: result.msg || 'Error al actualizar perfil' } });
            return;
        }

        // Usar directamente la respuesta del POST en lugar de hacer una nueva consulta
        if (result.user) {
            console.log('✅ Usando datos del POST directamente:', result.user);

            console.log('💾 Guardando en SecureStore:', result.user);
            await SecureStore.setItemAsync('user', JSON.stringify(result.user));

            console.log('🔄 Actualizando authState...');
            const newUserState = {
                user_id: result.user.user_id,
                name: result.user.name,
                last_name: result.user.last_name,
                phone_number: result.user.phone_number,
                email: result.user.email,
                company_id: result.user.company_id,
                company_name: result.user.company_name,
                employee_number: result.user.employee_number,
                curp: result.user.curp,
                id: result.user.user_id,
                photo: result.user.photo as any, // Usar la foto de la respuesta del POST
            };

            console.log('👤 Nuevo estado de usuario:', newUserState);

            setAuthState((prev) => ({
                ...prev,
                user: newUserState,
            }));

            // Actualizar también el estado local de la foto
            const finalPhoto = result.user.photo;
            if (finalPhoto) {
                console.log('🖼️ Actualizando photoUri después de guardar:', finalPhoto);
                setPhotoUri(finalPhoto as string);
                setFormData(prev => ({ ...prev, photo: finalPhoto }));
            }
        } else {
            console.warn('⚠️ La respuesta del POST no incluye datos de usuario, usando fallback');

            // Fallback: obtener el perfil actualizado
            console.log('🔄 Fallback - Obteniendo perfil actualizado...');
            const refreshedProfile = await getProfile();
            console.log('📥 Perfil actualizado recibido:', refreshedProfile);

            if (refreshedProfile.error) {
                console.error('❌ Error al obtener perfil actualizado:', refreshedProfile);
                showErrorAlert({
                    error: {
                        code: 'REFRESH_ERROR',
                        message: 'Los cambios se guardaron, pero no se pudo actualizar el perfil local.',
                    },
                });
                return;
            }

            console.log('💾 Guardando en SecureStore:', refreshedProfile.profile);
            await SecureStore.setItemAsync('user', JSON.stringify(refreshedProfile.profile));

            console.log('🔄 Actualizando authState...');
            const newUserState = {
                user_id: refreshedProfile.profile.user_id,
                name: refreshedProfile.profile.name,
                last_name: refreshedProfile.profile.last_name,
                phone_number: refreshedProfile.profile.phone_number,
                email: refreshedProfile.profile.email,
                company_id: refreshedProfile.profile.company_id,
                company_name: authState.user?.company_name,
                employee_number: refreshedProfile.profile.employee_number,
                curp: refreshedProfile.profile.curp,
                id: refreshedProfile.profile.user_id,
                photo: refreshedProfile.profile.photo as any,
            };

            console.log('👤 Nuevo estado de usuario:', newUserState);

            setAuthState((prev) => ({
                ...prev,
                user: newUserState,
            }));

            // Actualizar también el estado local de la foto
            const finalPhoto = refreshedProfile.profile.photo;
            if (finalPhoto) {
                console.log('🖼️ Actualizando photoUri después de guardar:', finalPhoto);
                setPhotoUri(finalPhoto as string);
                setFormData(prev => ({ ...prev, photo: finalPhoto }));
            }
        }

        console.log('✅ Perfil actualizado exitosamente');
        showSuccessAlert('✅ Perfil actualizado', 'Los cambios se guardaron correctamente.');
        setIsEditing(false);
    };



    // handleLogout removido - ahora se maneja automáticamente desde el Header



    return (
        <BaseScreen
            scroll={true}
            navColor={globalTheme.navigation_light}
        >
            <Header />
            <View style={styles.profileBox}>

                <View style={styles.bodyImage}>
                    <UserAvatar
                        uri={photoUri}
                        onPress={() => setShowPicker(isEditing)}
                        isEditing={isEditing}
                    />
                </View>

                <ProfileForm
                    formData={formData}
                    isEditing={isEditing}
                    setFormData={setFormData}
                />

                <ProfileActions
                    isEditing={isEditing}
                    onEditToggle={() => setIsEditing(true)}
                    onSaveProfile={handleSaveProfile}
                    onChangePassword={() => navigation.navigate('ChangePassword')}
                    onViewBeneficiaries={() => navigation.navigate('BeneficiariosScreen')}
                    onViewCredential={() => navigation.navigate('CredencialScreen')}
                />
            </View>

            <PhotoPickerModal
                visible={showPicker}
                onClose={() => {
                    console.log('📱 PhotoPickerModal cerrado');
                    setShowPicker(false);
                }}
                onPick={(uri) => {
                    console.log('📸 Foto seleccionada en ProfileScreen:', uri);
                    console.log('🔍 Detalles de la URI:');
                    console.log('  - Tipo:', typeof uri);
                    console.log('  - Longitud:', uri.length);
                    console.log('  - Empieza con file://:', uri.startsWith('file://'));

                    setPhotoUri(uri);
                    setFormData((prev) => {
                        const newFormData = { ...prev, photo: uri };
                        console.log('📝 FormData actualizado:', newFormData);
                        return newFormData;
                    });
                    console.log('✅ Estados actualizados - photoUri y formData.photo');
                }}
            />
        </BaseScreen>

    );
}
