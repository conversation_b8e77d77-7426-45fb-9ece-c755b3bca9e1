import React, {useEffect, useRef, useState} from 'react';
import {
    ActivityIndicator,
    Animated,
    ScrollView,
    View
} from 'react-native';
import BaseScreen from "../../components/BaseScreen";
import Header from "../../components/common/Header";
import * as Animatable from "react-native-animatable";
import CalendarComponent from "../../components/calendario/CalendarComponent";
import EventList from "../../components/calendario/EventList";
import TodayEvent from "../../components/calendario/TodayEvent";
import findEvents from "../../hooks/findEvents";
import {useEventos} from "../../hooks/useEventos";
import formatYMDWithOffset from "../../hooks/formatYMD";
import {useAuth} from "../../context/AuthContext";
import ErrorComponent from "../../components/common/ErrorComponents";

interface Evento {
    id: string;
    description: string;
    end_date: string;
    start_date: string;
    image: string;
    title: string;
    status: string
}

export default function CalendarScreen({navigation}) {
    const scrollRef = useRef<ScrollView>(null);
    const scaleAnim = useRef(new Animated.Value(1)).current;
    const [modalSuccessVisible, setModalSuccessVisible] = useState(false);
    const [calendarDaySelected, setCalendarDaysSelected] = useState<string>(new Date().toISOString().split('T')[0]);
    const [calendarIdSelected, setCalendarIdSelected] = useState<string[]>([''])
    const today = new Date();
    const { authState } = useAuth();

    //variables para control de fetch
    const [memory, setMemory] = useState<string[]>([]);
    const [accumulatedMarkedDates, setAccumulatedMarkedDates] = useState<{
        [p: string]: { dots: { key: string; color: string; selectedDotColor?: string; id: string }[] }
    }>({})
    const [eventos, setEventos] = useState<Evento[]>([]);



// en CalendarScreen.tsx
    const [dateRange, setDateRange] = useState({
        start: formatYMDWithOffset(today, 0, true),
        end:   formatYMDWithOffset(today, 0, false),
    });

    // handleLogout removido - ahora se maneja automáticamente desde el Header

    const { loading, error } = useEventos({
        companyId: authState?.user?.company_id,
        token: authState?.token,
        start_date: dateRange.start,
        end_date:   dateRange.end,
        amount:    '100',
        memory: memory,
        setMemory,
        eventos: eventos,
        setEventos,
        setAccumulatedMarkedDates,
    });

    const [calendarDaySelectedJson, setCalendarDaySelectedJson]= useState<Evento[]>(findEvents({
        ids: calendarIdSelected,
        date: calendarDaySelected,
        apiEvents: eventos,
    }))

    useEffect(() => {
        if (memory.length === 0) setModalSuccessVisible(true);
    }, [eventos]);


    useEffect(() => {
        const calendarDaySelectedJson: Evento[] = findEvents({
            ids: calendarIdSelected,
            date: calendarDaySelected,
            apiEvents: eventos,
        });

        setCalendarDaySelectedJson(calendarDaySelectedJson)
    }, [calendarDaySelected, calendarIdSelected]);


    useEffect(() => {
        // auto-scroll posts
        let scrollX = 0;
        const scrollInterval = setInterval(() => {
            scrollX += 300;
            scrollRef.current?.scrollTo({ x: scrollX, animated: true });
        }, 4000);

        return () => clearInterval(scrollInterval);
    }, []);

    useEffect(() => {
        // scale animation for banner
        Animated.loop(
            Animated.sequence([
                Animated.timing(scaleAnim, {
                    toValue: 1.03,
                    duration: 2000,
                    useNativeDriver: true,
                }),
                Animated.timing(scaleAnim, {
                    toValue: 1,
                    duration: 2000,
                    useNativeDriver: true,
                }),
            ])
        ).start();
    }, []);

    return (
        <BaseScreen
            scroll={true}
            chat={true}
            navColor={'#ffffff'}
        >
            <Header />

            <Animatable.View animation="fadeInUp" duration={800} delay={200}>

                <CalendarComponent
                    markedDates={accumulatedMarkedDates}
                    setCalendarDaySelected={setCalendarDaysSelected}
                    setCalendarIdSelected={setCalendarIdSelected}
                    setDataRange={setDateRange}
                />

                {!loading ? (
                    <>
                        {!error ? (
                            <View>

                                <TodayEvent eventos={calendarDaySelectedJson}/>

                                <EventList numeroEventos={eventos.length} eventos={eventos}/>

                                {/*<NewEventModal visible={modalSuccessVisible} setVisible={setModalSuccessVisible} eventos={eventos.length}/>*/}

                            </View>
                        ) : (
                            <ErrorComponent/>
                        )}
                    </>
                ) : (
                    <ActivityIndicator size={"large"} color={'#ffffff'}/>
                )}
            </Animatable.View>
        </BaseScreen>
    );
}

