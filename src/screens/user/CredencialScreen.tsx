import React, {useEffect, useState} from 'react';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    ImageSourcePropType,
    Pressable,
} from 'react-native';
import {useAuth} from '../../context/AuthContext';
import BaseScreen from '../../components/BaseScreen';
import {Ionicons} from '@expo/vector-icons';
import {credentialStyles as styles} from '../../styles/credentialStyles';
import UserAvatar from '../../components/common/UserAvatar';
import PhotoPickerModal from '../../components/common/PhotoPickerModal';
import LogoTS from "../../components/svg/LogoTS";
import {vs} from "react-native-size-matters";
import QrModal from "../../components/profile/QrModal";
import * as Brightness from "expo-brightness";
import useCredentialQr from "../../hooks/useCredentialQr";
import {globalTheme} from '../../constants/theme';

export default function CredencialScreen({navigation}: any) {
    const {authState} = useAuth();
    const user = authState.user;

    const [showPicker, setShowPicker] = useState(false);
    const [photoUri, setPhotoUri] = useState<ImageSourcePropType>(user?.photo || null);

    const [modalQr, setModalQr] = useState<boolean | null>(null);

    const [link, setLink] = useState<string | null>(null);


    const background = require('../../../assets/images/credencial.png')
    const signature = require('../../../assets/images/signature.png')

    const [previousBrightness, setPreviousBrightness] = useState<number>();

    const handleModalQr = async () => {
        const { granted } = await Brightness.requestPermissionsAsync();

        if (granted) {
            const prev = await Brightness.getSystemBrightnessAsync();
            setPreviousBrightness(prev);
            await Brightness.setSystemBrightnessAsync(1);
        }

        await fetchQr(user.email); // ✅ Generar token justo al abrir
        setModalQr(true);
    };


    const {
        loadingQr ,
        errorQr,
        fetchQr
    } = useCredentialQr({
        setLink
    })


    return (
        <BaseScreen
            scroll={false}
            navColor={globalTheme.gradient[1] }
        >

            <View style={styles.card}>

                <Image
                    style={{width: '100%', height: '100%'}}
                    source={background}/>

                <View style={styles.cardContainer}>
                    <LogoTS size={"sm"}/>


                    <View style={styles.containerBody}>

                        <View style={styles.bodyImage}>
                            <UserAvatar
                                uri={photoUri}
                                style={styles.avatar}
                                onPress={() => setShowPicker(true)}
                            />

                            <PhotoPickerModal
                                visible={showPicker}
                                onClose={() => setShowPicker(false)}
                                onPick={(uri) => {
                                    setPhotoUri(uri);
                                }}
                            />
                        </View>

                        <Text style={styles.label}>Nombre</Text>
                        <Text style={styles.info}>
                            {user?.name} {user?.last_name}
                        </Text>
                        <Text style={styles.label}>NÚMERO DE TELÉFONO</Text>
                        <Text style={styles.info}>{user?.phone_number}</Text>
                        <Text style={styles.label}>CORREO ELECTRÓNICO</Text>
                        <Text style={styles.info}>{user?.email}</Text>
                        <Text style={styles.label}>EMPRESA</Text>
                        <Text style={styles.info}>{user?.company_name}</Text>
                        <Text style={styles.label}>NÚMERO DE EMPLEADO</Text>
                        <Text style={styles.info}>Nº {user?.employee_number}</Text>
                    </View>


                        <View style={{flexDirection: "column", width: '100%', justifyContent: 'space-evenly', alignItems: 'center'}}>
                            <Image source={signature} style={styles.signature}/>

                            <Pressable
                                style={styles.qrButton}
                                onPress={() => handleModalQr()}
                            >
                                <Text style={{color: '#FFF', fontSize: vs(14)}}>
                                    Ver código
                                </Text>
                            </Pressable>

                        </View>

                </View>
            </View>


            <TouchableOpacity style={styles.backTop} onPress={() => navigation.goBack()}>
                <Ionicons name="reload" size={vs(22)} color="#fff"/>
                <Text style={styles.backText}>Regresar</Text>
            </TouchableOpacity>

            <QrModal
                previousBrightness={previousBrightness}
                loadingQr={loadingQr}
                errorQr={errorQr}
                link={link}
                modalQr={modalQr}
                setModalQr={setModalQr}/>

        </BaseScreen>
    );
}
