import React, { useEffect } from 'react';
import { View } from 'react-native';
import * as Animatable from 'react-native-animatable';
import { LinearGradient } from 'expo-linear-gradient';
import { splashStyles as styles } from '../styles/splashStyles';
import {globalTheme} from "../constants/theme";
import LogoTS from "../components/svg/LogoTS";
import changeNavigationBarColor from 'react-native-navigation-bar-color';

export default function SplashScreen({ onFinish }: { onFinish: () => void }) {
    useEffect(() => {
        const timer = setTimeout(onFinish, 3000);
        return () => clearTimeout(timer);
    }, [onFinish]);

    useEffect(() => {
        changeNavigationBarColor(globalTheme.gradient[1], true);
    }, []);

    return (
        <View style={styles.container}>
            <LinearGradient
                colors={[globalTheme.gradient[0], globalTheme.gradient[1]]}
                style={styles.gradient}
            />
            <Animatable.View
                animation="bounceIn"
                duration={2000}
                style={styles.logoContainer}
            >
                <LogoTS size={"lg"}/>
            </Animatable.View>
        </View>
    );
}
