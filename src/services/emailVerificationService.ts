// src/services/emailVerificationService.ts

import axios from 'axios';
import { API_URL } from '../constants/config';

/**
 * Reenvía un nuevo código de verificación al correo del usuario.
 */
export const resendEmailVerification = async (userId: number) => {
    console.log("[resendEmailVerification] Solicitando reenvío:", {
        userId,
        typeOfUserId: typeof userId
    });

    try {
        const url = `${API_URL}/users/${userId}/email-verification/resend`;
        console.log("[resendEmailVerification] URL:", url);

        const { data } = await axios.post(url);

        console.log("[resendEmailVerification] Respuesta exitosa:", data);
        return { success: true };
    } catch (error: any) {
        console.error('❌ [resendEmailVerification] Error completo:', error);

        const errorData = {
            message: error.response?.data?.error?.message || error.message,
            status: error.response?.status,
            requestConfig: error.config
        };

        console.error('❌ [resendEmailVerification] Detalles del error:', errorData);
        return {
            success: false,
            message: errorData.message || 'No se pudo reenviar el código'
        };
    }
};

/**
 * Verifica si el código de verificación ingresado es correcto.
 */
export const verifyEmailCode = async (userId: number, verificationCode: string) => {
    console.log("[verifyEmailCode] Verificando código:", {
        userId,
        verificationCode,
        typeOfUserId: typeof userId
    });

    try {
        const url = `${API_URL}/users/${userId}/email-verification`;
        console.log("[verifyEmailCode] URL:", url);

        const { data } = await axios.patch(url, {
            verification_code: verificationCode,
        });

        console.log("[verifyEmailCode] Respuesta exitosa:", data);
        return { success: true };
    } catch (error: any) {
        console.error('❌ [verifyEmailCode] Error completo:', error);

        const errorData = {
            message: error.response?.data?.error?.message || error.message,
            status: error.response?.status,
            requestConfig: error.config
        };

        console.error('❌ [verifyEmailCode] Detalles del error:', errorData);

        // Si el error es que el usuario ya está verificado, lo consideramos como éxito
        if (error.response?.status === 400 &&
            error.response?.data?.error?.message === "El usuario ya está verificado.") {
            console.log("[verifyEmailCode] Usuario ya verificado, procediendo como éxito");
            return {
                success: true,
                alreadyVerified: true
            };
        }

        return {
            success: false,
            message: errorData.message || 'Código inválido'
        };
    }
};

/**
 * Cambia la contraseña del usuario después de verificar su correo.
 */
export const resetPassword = async (userId: number, newPassword: string) => {
    console.log("[resetPassword] Cambiando contraseña:", {
        userId,
        typeOfUserId: typeof userId
    });

    try {
        const url = `${API_URL}/users/${userId}/reset-password`;
        console.log("[resetPassword] URL:", url);

        const { data } = await axios.patch(url, {
            new_password: newPassword,
        });

        console.log("[resetPassword] Respuesta exitosa:", data);
        return { success: true };
    } catch (error: any) {
        console.error('❌ [resetPassword] Error completo:', error);

        const errorData = {
            message: error.response?.data?.message || error.message,
            status: error.response?.status,
            requestConfig: error.config
        };

        console.error('❌ [resetPassword] Detalles del error:', errorData);
        return {
            success: false,
            message: errorData.message || 'No se pudo restablecer la contraseña'
        };
    }
};

/**
 * Inicia el proceso de recuperación de contraseña por email
 * POST /users/password-reset/email
 * Body: {"email": "<EMAIL>"}
 */
export const initiatePasswordResetByEmail = async (email: string) => {
    console.log("[initiatePasswordResetByEmail] Iniciando recuperación:", {
        email,
        typeOfEmail: typeof email
    });

    try {
        const url = `${API_URL}/password-reset/email`;
        console.log("[initiatePasswordResetByEmail] URL:", url);

        const { data } = await axios.post(url, {
            email: email
        });

        console.log("[initiatePasswordResetByEmail] Respuesta exitosa:", data);
        return {
            success: true,
            user_id: data.user_id,
            message: data.message
        };
    } catch (error: any) {
        console.error('❌ [initiatePasswordResetByEmail] Error completo:', error);

        const errorData = {
            message: error.response?.data?.message || error.response?.data?.error?.message || error.message,
            status: error.response?.status,
            code: error.response?.data?.error_code || error.response?.data?.error?.code,
            requestConfig: error.config
        };

        console.error('❌ [initiatePasswordResetByEmail] Detalles del error:', errorData);
        return {
            success: false,
            message: errorData.message || 'No se pudo enviar el código de verificación',
            code: errorData.code
        };
    }
};

/**
 * Reenviar código de email para recuperación de contraseña
 * Diferente del reenvío de verificación de registro
 */
export const resendPasswordResetEmailCode = async (userId: number) => {
    console.log("[resendPasswordResetEmailCode] Solicitando reenvío de código de recuperación:", {
        userId,
        typeOfUserId: typeof userId
    });

    try {
        const url = `${API_URL}/password-reset/email/resend`;
        console.log("[resendPasswordResetEmailCode] URL:", url);

        const { data } = await axios.post(url, {
            user_id: userId
        });

        console.log("[resendPasswordResetEmailCode] Código de recuperación reenviado:", data);
        return { success: true };
    } catch (error: any) {
        console.error('❌ [resendPasswordResetEmailCode] Error completo:', error);

        const errorData = {
            message: error.response?.data?.message || error.response?.data?.error?.message || error.message,
            status: error.response?.status,
            data: error.response?.data,
            requestConfig: error.config
        };

        console.error('❌ [resendPasswordResetEmailCode] Detalles del error:', errorData);

        // Manejo específico de errores
        let userMessage = "No se pudo reenviar el código";

        if (error.response?.status === 404) {
            userMessage = "Usuario no encontrado o sesión expirada.";
        } else if (error.response?.status === 400) {
            userMessage = errorData.message || "Solicitud inválida.";
        } else if (error.response?.status === 500) {
            userMessage = "Error del servidor. Intenta de nuevo más tarde.";
        } else if (!error.response) {
            userMessage = "Error de conexión. Verifica tu internet.";
        } else {
            userMessage = errorData.message || "No se pudo reenviar el código.";
        }

        return {
            success: false,
            message: userMessage,
            status: error.response?.status
        };
    }
};

/**
 * Verificar código de email para recuperación de contraseña
 * Diferente del endpoint de verificación de registro
 */
export const verifyPasswordResetEmailCode = async (userId: number, verificationCode: string) => {
    console.log("[verifyPasswordResetEmailCode] Iniciando verificación de código de recuperación:", {
        userId,
        verificationCode,
        typeOfUserId: typeof userId
    });

    try {
        const url = `${API_URL}/password-reset/email/verify`;
        console.log("[verifyPasswordResetEmailCode] URL:", url);

        const { data } = await axios.patch(url, {
            user_id: userId,
            verification_code: verificationCode
        });

        console.log("[verifyPasswordResetEmailCode] Respuesta exitosa:", data);
        return { success: true };
    } catch (error: any) {
        console.error('❌ [verifyPasswordResetEmailCode] Error completo:', error);

        const errorData = {
            message: error.response?.data?.message || error.response?.data?.error?.message || error.message,
            status: error.response?.status,
            data: error.response?.data,
            requestConfig: error.config
        };

        console.error('❌ [verifyPasswordResetEmailCode] Detalles del error:', errorData);

        // Manejo específico de errores
        let userMessage = "Código inválido";
        let errorCode = 'GENERIC_ERROR';

        if (error.response?.status === 400) {
            if (errorData.message?.includes('ya está verificado')) {
                return {
                    success: true,
                    alreadyVerified: true
                };
            } else if (errorData.message?.includes('inválido') || errorData.message?.includes('invalid')) {
                errorCode = 'INVALID_CODE';
                userMessage = "El código ingresado es inválido.";
            } else if (errorData.message?.includes('expirado') || errorData.message?.includes('expired')) {
                errorCode = 'EXPIRED_CODE';
                userMessage = "El código ha expirado. Solicita uno nuevo.";
            } else {
                userMessage = errorData.message || "Código inválido.";
            }
        } else if (error.response?.status === 404) {
            errorCode = 'USER_NOT_FOUND';
            userMessage = "Usuario no encontrado o sesión expirada.";
        } else if (error.response?.status === 500) {
            errorCode = 'SERVER_ERROR';
            userMessage = "Error del servidor. Intenta de nuevo más tarde.";
        } else if (!error.response) {
            errorCode = 'NETWORK_ERROR';
            userMessage = "Error de conexión. Verifica tu internet.";
        } else {
            userMessage = errorData.message || "No se pudo verificar el código.";
        }

        return {
            success: false,
            message: userMessage,
            errorCode: errorCode,
            status: error.response?.status
        };
    }
};
