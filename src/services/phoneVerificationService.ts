import axios from "axios";
import { API_URL } from "../constants/config";

/**
 * Paso 1: Verificar número de teléfono y enviar código SMS
 */
export const verifyPhoneNumber = async (phoneNumber: string) => {
    try {
        const url = `${API_URL}/verify-phone-number`;
        const { data } = await axios.post(url, {
            phone_number: phoneNumber
        });

        return {
            success: true,
            userId: data.user_id,
            message: data.message
        };
    } catch (error) {
        console.error("❌ [verifyPhoneNumber] Error:", error.response?.data || error.message);

        // Manejo específico de códigos de error PVC del backend
        const backendErrorCode = error.response?.data?.error?.code;
        const backendMessage = error.response?.data?.error?.message || error.message;

        // Si es un código PVC específico, lo manejamos directamente
        if (backendErrorCode && backendErrorCode.startsWith('PVC-')) {
            return {
                success: false,
                message: backendMessage,
                errorCode: backendErrorCode,
                status: error.response?.status
            };
        }

        // Fallback para errores sin código PVC específico
        let errorCode = 'PHONE_VERIFICATION_ERROR';
        let userMessage = backendMessage || "Error al verificar el número de teléfono";

        if (error.response?.status === 500) {
            errorCode = 'SERVER_ERROR_500';
            userMessage = "Error del servidor. Intenta de nuevo en unos momentos.";
        } else if (error.response?.status === 400) {
            errorCode = 'VALIDATION_ERROR';
            userMessage = backendMessage || "Número de teléfono inválido.";
        } else if (error.response?.status === 404) {
            errorCode = 'PVC-011'; // Teléfono no encontrado
            userMessage = "No se encontró un usuario con este número de teléfono.";
        } else if (error.response?.status === 408 || error.code === 'ECONNABORTED') {
            errorCode = 'SERVER_ERROR_TIMEOUT';
            userMessage = "La solicitud tardó demasiado tiempo. Intenta de nuevo.";
        } else if (!error.response) {
            errorCode = 'NETWORK_ERROR';
            userMessage = "Error de conexión. Verifica tu internet.";
        }

        return {
            success: false,
            message: userMessage,
            errorCode: errorCode,
            status: error.response?.status
        };
    }
};

export const verifyPhoneCode = async (userId: number, verificationCode: string) => {
    try {
        const url = `${API_URL}/users/${userId}/phone-verification`;
        await axios.patch(url, {
            verification_code: verificationCode
        });

        return { success: true };
    } catch (error) {
        console.error("❌ [verifyPhoneCode] Error:", error.response?.data || error.message);

        // Manejo específico de códigos de error PVC del backend
        const backendErrorCode = error.response?.data?.error?.code;
        const backendMessage = error.response?.data?.error?.message || error.message;

        // Si es un código PVC específico, lo manejamos directamente
        if (backendErrorCode && backendErrorCode.startsWith('PVC-')) {
            // PVC-002 (usuario ya verificado) lo consideramos como éxito
            if (backendErrorCode === 'PVC-002') {
                return {
                    success: true,
                    alreadyVerified: true
                };
            }

            return {
                success: false,
                message: backendMessage,
                errorCode: backendErrorCode,
                status: error.response?.status
            };
        }

        // Fallback para errores sin código PVC específico
        let errorCode = 'VERIFICATION_CODE_INVALID';
        let userMessage = backendMessage || "Código inválido";

        if (error.response?.status === 500) {
            errorCode = 'SERVER_ERROR_500';
            userMessage = "Error del servidor. Intenta de nuevo en unos momentos.";
        } else if (error.response?.status === 400) {
            // Verificar mensajes específicos para compatibilidad con versiones anteriores
            if (backendMessage.includes('ya está verificado') || backendMessage.includes('already verified')) {
                return {
                    success: true,
                    alreadyVerified: true
                };
            } else if (backendMessage.includes('expirado') || backendMessage.includes('expired')) {
                errorCode = 'VERIFICATION_CODE_EXPIRED';
                userMessage = "El código ha expirado. Solicita un nuevo código.";
            } else if (backendMessage.includes('inválido') || backendMessage.includes('invalid') || backendMessage.includes('incorrecto')) {
                errorCode = 'VERIFICATION_CODE_INVALID';
                userMessage = "El código ingresado no es válido.";
            }
        } else if (error.response?.status === 404) {
            errorCode = 'PVC-001'; // Usuario no encontrado
            userMessage = "El usuario no existe o no está activo.";
        } else if (error.response?.status === 408 || error.code === 'ECONNABORTED') {
            errorCode = 'SERVER_ERROR_TIMEOUT';
            userMessage = "La solicitud tardó demasiado tiempo. Intenta de nuevo.";
        } else if (!error.response) {
            errorCode = 'NETWORK_ERROR';
            userMessage = "Error de conexión. Verifica tu internet.";
        }

        return {
            success: false,
            message: userMessage,
            errorCode: errorCode,
            status: error.response?.status
        };
    }
};

export const resendPhoneVerification = async (userId: number) => {
    try {
        const url = `${API_URL}/users/${userId}/phone-verification/resend`;
        await axios.post(url);
        return { success: true };
    } catch (error: any) {
        console.error("❌ [resendPhoneVerification] Error:", error.response?.data || error.message);

        // Manejo específico para diferentes tipos de errores
        let errorCode = 'GENERIC_ERROR';
        let userMessage = "No se pudo reenviar el código";

        if (error.response?.status === 500) {
            errorCode = 'SERVER_ERROR_500';
            userMessage = "Error del servidor. Intenta de nuevo en unos momentos.";
        } else if (error.response?.status === 429) {
            errorCode = 'RATE_LIMIT_EXCEEDED';
            userMessage = "Has solicitado demasiados códigos. Espera un momento antes de intentar de nuevo.";
        } else if (error.response?.status === 404) {
            errorCode = 'AUTH_USER_NOT_FOUND';
            userMessage = "Usuario no encontrado.";
        } else if (error.response?.status === 408 || error.code === 'ECONNABORTED') {
            errorCode = 'SERVER_ERROR_TIMEOUT';
            userMessage = "La solicitud tardó demasiado tiempo. Intenta de nuevo.";
        } else if (!error.response) {
            errorCode = 'NETWORK_ERROR';
            userMessage = "Error de conexión. Verifica tu internet.";
        } else {
            userMessage = error.response?.data?.error?.message || error.message || "No se pudo reenviar el código";
        }

        return {
            success: false,
            message: userMessage,
            errorCode: errorCode,
            status: error.response?.status
        };
    }
};

export const initiatePasswordResetByPhone = async (phone_number: string) => {
    try {
        const url = `${API_URL}/password-reset/phone`;
        const { data } = await axios.post(url, { phone_number });

        // Extraer el user_id de la respuesta
        const userId = data.user_id;
        if (!userId) {
            return {
                success: false,
                message: "Error en la respuesta del servidor"
            };
        }

        return { 
            success: true, 
            data: {
                userId: userId,
                message: data.message
            }
        };
    } catch (error: any) {
        console.error("❌ [initiatePasswordReset] Error:", error.response?.data || error.message);

        // Manejo específico de errores
        let userMessage = "No se pudo iniciar la recuperación";

        if (error.response?.status === 404) {
            userMessage = "El número de teléfono no está registrado en el sistema.";
        } else if (error.response?.status === 400) {
            userMessage = error.response?.data?.message || error.response?.data?.error?.message || "Número de teléfono inválido.";
        } else if (error.response?.status === 500) {
            userMessage = "Error del servidor. Intenta de nuevo más tarde.";
        } else if (!error.response) {
            userMessage = "Error de conexión. Verifica tu internet.";
        } else {
            userMessage = error.response?.data?.message || error.response?.data?.error?.message || error.message || "No se pudo enviar el código SMS.";
        }

        return {
            success: false,
            message: userMessage,
            error_code: error.response?.data?.error_code || error.response?.data?.error?.code,
            status: error.response?.status
        };
    }
};

/**
 * Reenviar código SMS para recuperación de contraseña
 * Diferente del reenvío de verificación de registro
 */
export const resendPasswordResetCode = async (userId: number) => {
    try {
        const url = `${API_URL}/password-reset/resend`;
        await axios.post(url, {
            user_id: userId
        });
        return { success: true };
    } catch (error: any) {
        console.error("❌ [resendPasswordResetCode] Error:", error.response?.data || error.message);

        // Manejo específico de errores
        let userMessage = "No se pudo reenviar el código";

        if (error.response?.status === 404) {
            userMessage = "Usuario no encontrado o sesión expirada.";
        } else if (error.response?.status === 400) {
            userMessage = error.response?.data?.message || error.response?.data?.error?.message || "Solicitud inválida.";
        } else if (error.response?.status === 500) {
            userMessage = "Error del servidor. Intenta de nuevo más tarde.";
        } else if (!error.response) {
            userMessage = "Error de conexión. Verifica tu internet.";
        } else {
            userMessage = error.response?.data?.message || error.response?.data?.error?.message || error.message || "No se pudo reenviar el código SMS.";
        }

        return {
            success: false,
            message: userMessage,
            status: error.response?.status
        };
    }
};

/**
 * Verificar código SMS para recuperación de contraseña
 * Diferente del endpoint de verificación de registro
 */
export const verifyPasswordResetCode = async (userId: number, verificationCode: string) => {
    try {
        const url = `${API_URL}/password-reset/verify`;
        await axios.patch(url, {
            user_id: userId,
            verification_code: verificationCode
        });
        return { success: true };
    } catch (error: any) {
        console.error("❌ [verifyPasswordResetCode] Error:", error.response?.data || error.message);

        // Manejo específico de errores
        let userMessage = "Código inválido";
        let errorCode = 'GENERIC_ERROR';

        const errorMessage = error.response?.data?.message || error.response?.data?.error?.message || error.message;

        if (error.response?.status === 400) {
            if (errorMessage?.includes('ya está verificado')) {
                return {
                    success: true,
                    alreadyVerified: true
                };
            } else if (errorMessage?.includes('inválido') || errorMessage?.includes('invalid')) {
                errorCode = 'INVALID_CODE';
                userMessage = "El código ingresado es inválido.";
            } else if (errorMessage?.includes('expirado') || errorMessage?.includes('expired')) {
                errorCode = 'EXPIRED_CODE';
                userMessage = "El código ha expirado. Solicita uno nuevo.";
            } else {
                userMessage = errorMessage || "Código inválido.";
            }
        } else if (error.response?.status === 404) {
            errorCode = 'USER_NOT_FOUND';
            userMessage = "Usuario no encontrado o sesión expirada.";
        } else if (error.response?.status === 500) {
            errorCode = 'SERVER_ERROR';
            userMessage = "Error del servidor. Intenta de nuevo más tarde.";
        } else if (!error.response) {
            errorCode = 'NETWORK_ERROR';
            userMessage = "Error de conexión. Verifica tu internet.";
        } else {
            userMessage = errorMessage || "No se pudo verificar el código.";
        }

        return {
            success: false,
            message: userMessage,
            errorCode: errorCode,
            status: error.response?.status
        };
    }
};