// Funciones actualizadas para React Native API
import { API_URL } from '../constants/config';
import { prepareFormDataForSubmission, validateFormValues } from './fileService';
import axios from 'axios';

export async function listForms(tenant: string, token: string) {
  console.log('[listForms] tenant:', tenant, 'token:', token);
  try {
    const url = `${API_URL.replace(/\/$/, '')}/forms`;
    console.log('[listForms] GET', url);
    const { data } = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
    });
    console.log('[listForms] response:', data);
    return data.forms;
  } catch (error: any) {
    console.log('[listForms] error:', error);
    const msg = error.response?.data?.error || error.message;
    throw new Error(msg);
  }
}

export async function getFormDetails(tenant: string, token: string, formId: number) {
  console.log('[getFormDetails] tenant:', tenant, 'token:', token, 'formId:', formId);
  try {
    const url = `${API_URL.replace(/\/$/, '')}/forms/${formId}`;
    console.log('[getFormDetails] GET FormDetails', url);
    const { data } = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
    });
    console.log('[getFormDetails] response:', data);

    // Log específico para valores de archivos
    if (data.user_submission_status?.submission?.values) {
      console.log('[getFormDetails] submission values:', JSON.stringify(data.user_submission_status.submission.values, null, 2));
    }

    return data;
  } catch (error: any) {
    console.log('[getFormDetails] error:', error);
    const msg = error.response?.data?.error || error.message;
    throw new Error(msg);
  }
}

export async function submitForm(
  tenant: string,
  token: string,
  formId: number,
  values: Record<string, any>
) {
  const timestamp = new Date().toISOString();
  console.log(`🚀 [${timestamp}] === INICIANDO submitForm ===`);
  console.log(`📊 [${timestamp}] Parámetros:`, {
    tenant,
    token: token ? `${token.substring(0, 10)}...` : 'MISSING',
    formId,
    valuesCount: Object.keys(values).length
  });

  // Log detallado de valores
  console.log(`📝 [${timestamp}] Valores recibidos:`, values);

  // Verificar si hay archivos para subir
  const fileValues = Object.entries(values).filter(([key, value]) =>
    typeof value === 'string' && (value.startsWith('file://') || value.startsWith('content://'))
  );
  const hasFiles = fileValues.length > 0;

  console.log(`🔍 [${timestamp}] Análisis de archivos:`, {
    hasFiles,
    fileCount: fileValues.length,
    fileFields: fileValues.map(([key, value]) => ({
      field: key,
      uri: typeof value === 'string' ? value.substring(0, 50) + '...' : value,
      type: typeof value === 'string' && value.startsWith('file://') ? 'file://' :
            typeof value === 'string' && value.startsWith('content://') ? 'content://' : 'other'
    }))
  });

  if (hasFiles) {
    console.log(`📎 [${timestamp}] Detectados archivos para subir, usando FormData`);

    // Validar valores del formulario - TEMPORAL: Solo permitir file:// URIs
    console.log(`🔍 [${timestamp}] Validando valores del formulario...`);
    const validation = validateFormValues(values);
    console.log(`✅ [${timestamp}] Resultado de validación:`, validation);

    if (validation.warnings.length > 0) {
      console.warn(`⚠️ [${timestamp}] Advertencias en formulario:`, validation.warnings);
    }
    if (!validation.isValid) {
      console.error(`❌ [${timestamp}] Errores de validación:`, validation.errors);
      throw new Error(`Errores en formulario: ${validation.errors.join(', ')}`);
    }

    // TEMPORAL: Rechazar content:// URIs si react-native-fs no está disponible
    const contentUriValues = Object.values(values).filter(value =>
      typeof value === 'string' && value.startsWith('content://')
    );
    const hasContentUris = contentUriValues.length > 0;

    console.log(`🔍 [${timestamp}] Verificación de content URIs:`, {
      hasContentUris,
      contentUriCount: contentUriValues.length
    });

    if (hasContentUris) {
      // Verificar si react-native-fs está disponible
      try {
        const RNFS = require('react-native-fs');
        if (!RNFS || typeof RNFS.copyFile !== 'function') {
          console.error(`❌ [${timestamp}] react-native-fs no disponible`);
          throw new Error('react-native-fs no disponible');
        }
        console.log(`✅ [${timestamp}] react-native-fs disponible`);
      } catch (error) {
        console.error(`❌ [${timestamp}] Error verificando react-native-fs:`, error);
        throw new Error('Por el momento solo se pueden subir fotos desde la cámara o galería. Los documentos requieren rebuildar la aplicación.');
      }
    }

    // Usar la nueva función para preparar FormData correctamente (ahora es asíncrona)
    console.log(`📦 [${timestamp}] Preparando FormData...`);
    const formData = await prepareFormDataForSubmission(values);
    console.log(`✅ [${timestamp}] FormData preparado exitosamente`);

    // FormData ya está preparado con logs internos

    try {
      const url = `${API_URL.replace(/\/$/, '')}/forms/${formId}/submit`;
      console.log(`🌐 [${timestamp}] POST (con archivos) a:`, url);
      console.log(`📋 [${timestamp}] Headers:`, {
        'Authorization': `Bearer ${token ? token.substring(0, 10) + '...' : 'MISSING'}`,
        'Content-Type': 'multipart/form-data'
      });
      console.log(`⏱️ [${timestamp}] Timeout configurado: 30000ms`);

      const response = await axios.post(url, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
        timeout: 30000, // 30 segundos para uploads
      });

      console.log(`✅ [${timestamp}] Respuesta HTTP exitosa:`, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data
      });

      return response.data;
    } catch (error: any) {
      console.error(`❌ [${timestamp}] Error en POST (con archivos):`, {
        message: error.message,
        code: error.code,
        stack: error.stack
      });

      if (error.response) {
        console.error(`📡 [${timestamp}] Respuesta de error del servidor:`, {
          status: error.response.status,
          statusText: error.response.statusText,
          headers: error.response.headers,
          data: error.response.data
        });
      } else if (error.request) {
        console.error(`📡 [${timestamp}] Error de red (sin respuesta):`, error.request);
      } else {
        console.error(`⚙️ [${timestamp}] Error de configuración:`, error.message);
      }

      // Manejo especial para cuando el usuario ya ha enviado el formulario
      if (error.response?.status === 409) {
        const errorData = error.response.data;
        console.log(`⚠️ [${timestamp}] Error 409 - Formulario ya enviado:`, errorData);
        throw new Error(JSON.stringify({
          type: 'ALREADY_SUBMITTED',
          message: errorData.error,
          existingSubmissionId: errorData.existing_submission_id,
          submittedAt: errorData.submitted_at
        }));
      }

      // Manejo específico de errores de content URI
      if (error.response?.data?.error === 'Android content URI detected') {
        console.error(`❌ [${timestamp}] Error de content URI detectado`);
        throw new Error('Hay un problema con el archivo seleccionado. Por favor, selecciona el archivo nuevamente.');
      }

      // Manejo de errores de permisos del servidor
      if (error.response?.data?.message?.includes('Permission denied')) {
        console.error(`❌ [${timestamp}] Error de permisos del servidor`);
        throw new Error('Error del servidor: No se pueden guardar archivos. Contacta al administrador.');
      }

      // Manejo de errores de upload de archivos
      if (error.response?.data?.error === 'Error uploading file') {
        console.error(`❌ [${timestamp}] Error subiendo archivo`);
        throw new Error('Error subiendo archivo. El archivo puede estar dañado o ser de un tipo no soportado. Intenta con otro archivo.');
      }

      // Manejo de errores relacionados con react-native-fs
      if (error.message?.includes('react-native-fs') || error.message?.includes('RNSFILETYPEREGULAR')) {
        throw new Error('Error de compatibilidad de archivos. Por favor, usa solo fotos de la cámara o galería por el momento.');
      }

      const msg = error.response?.data?.error || error.message;
      throw new Error(msg);
    }
  } else {
    // Sin archivos, usar JSON normal
    console.log(`📝 [${timestamp}] Sin archivos detectados, usando JSON`);

    // Procesar valores para envío
    console.log(`🔄 [${timestamp}] Procesando valores para envío JSON...`);
    const processedValues: Record<string, any> = {};
    Object.entries(values).forEach(([fieldId, value]) => {
      if (Array.isArray(value)) {
        // Arrays (checkboxes) - convertir a JSON string
        processedValues[fieldId] = JSON.stringify(value);
        console.log(`☑️ [${timestamp}] Campo array ${fieldId}:`, {
          original: value,
          processed: processedValues[fieldId]
        });
      } else {
        processedValues[fieldId] = value || '';
        console.log(`📝 [${timestamp}] Campo ${fieldId}:`, {
          original: value,
          processed: processedValues[fieldId]
        });
      }
    });

    console.log(`📊 [${timestamp}] Valores procesados finales:`, processedValues);

    try {
      const url = `${API_URL.replace(/\/$/, '')}/forms/${formId}/submit`;
      const requestBody = { values: processedValues };

      console.log(`🌐 [${timestamp}] POST (JSON) a:`, url);
      console.log(`📋 [${timestamp}] Headers:`, {
        'Authorization': `Bearer ${token ? token.substring(0, 10) + '...' : 'MISSING'}`
      });
      console.log(`📦 [${timestamp}] Request body:`, requestBody);

      const response = await axios.post(
        url,
        requestBody,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      console.log(`✅ [${timestamp}] Respuesta HTTP exitosa (JSON):`, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data
      });

      return response.data;
    } catch (error: any) {
      console.error(`❌ [${timestamp}] Error en POST (JSON):`, {
        message: error.message,
        code: error.code,
        stack: error.stack
      });

      if (error.response) {
        console.error(`📡 [${timestamp}] Respuesta de error del servidor (JSON):`, {
          status: error.response.status,
          statusText: error.response.statusText,
          headers: error.response.headers,
          data: error.response.data
        });
      } else if (error.request) {
        console.error(`📡 [${timestamp}] Error de red (sin respuesta, JSON):`, error.request);
      } else {
        console.error(`⚙️ [${timestamp}] Error de configuración (JSON):`, error.message);
      }

      // Manejo especial para cuando el usuario ya ha enviado el formulario
      if (error.response?.status === 409) {
        const errorData = error.response.data;
        console.log(`⚠️ [${timestamp}] Error 409 - Formulario ya enviado (JSON):`, errorData);
        throw new Error(JSON.stringify({
          type: 'ALREADY_SUBMITTED',
          message: errorData.error,
          existingSubmissionId: errorData.existing_submission_id,
          submittedAt: errorData.submitted_at
        }));
      }

      // Para errores 500, agregar más contexto
      if (error.response?.status === 500) {
        console.error(`🔥 [${timestamp}] Server error 500 - Detalles de la petición:`);
        console.error(`- URL:`, `${API_URL.replace(/\/$/, '')}/forms/${formId}/submit`);
        console.error(`- Processed values:`, processedValues);
        console.error(`- Original values:`, values);
        console.error(`- Tenant:`, tenant);
        console.error(`- Form ID:`, formId);
      }

      const msg = error.response?.data?.error || error.message;
      console.error(`❌ [${timestamp}] Error final (JSON):`, msg);
      throw new Error(msg);
    }
  }

  console.log(`🏁 [${timestamp}] === FIN submitForm ===`);
}

// Nueva función para obtener la respuesta existente del usuario
export async function getMySubmission(
  tenant: string,
  token: string,
  formId: number
) {
  console.log('[getMySubmission] tenant:', tenant, 'token:', token, 'formId:', formId);
  try {
    const url = `${API_URL.replace(/\/$/, '')}/forms/${formId}/my-submission`;
    console.log('[getMySubmission] GET', url);
    const { data } = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
    });
    console.log('[getMySubmission] response:', data);
    return data;
  } catch (error: any) {
    console.log('[getMySubmission] error:', error);
    const msg = error.response?.data?.error || error.message;
    throw new Error(msg);
  }
}

// Función helper para verificar si el usuario puede enviar el formulario
export function canUserSubmitForm(formDetails: any): boolean {
  return formDetails.user_submission_status?.can_submit === true;
}

// Función helper para verificar si el usuario ya envió el formulario
export function hasUserSubmittedForm(formDetails: any): boolean {
  return formDetails.user_submission_status?.has_submitted === true;
}

// Función helper para obtener información de la respuesta del usuario
export function getUserSubmissionInfo(formDetails: any): any {
  return formDetails.user_submission_status?.submission || null;
}
