import { API_URL } from '../constants/config';

// Importación condicional de react-native-fs
let RNFS: any = null;
try {
  RNFS = require('react-native-fs');
} catch (error) {
  console.warn('⚠️ react-native-fs no disponible:', error);
  RNFS = null;
}

export interface FileUploadResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  error?: string;
}

export interface FileMetadata {
  uri: string;
  name: string;
  type: string;
  size?: number;
}

/**
 * Extrae metadatos de un archivo desde su URI
 */
export const getFileMetadata = async (uri: string): Promise<FileMetadata> => {
  try {
    // Para archivos locales (file://)
    if (uri.startsWith('file://')) {
      const fileName = uri.split('/').pop() || 'archivo';
      const extension = fileName.split('.').pop()?.toLowerCase() || '';
      
      // Determinar tipo MIME basado en extensión
      const mimeType = getMimeTypeFromExtension(extension);
      
      return {
        uri,
        name: fileName,
        type: mimeType,
      };
    }
    
    // Para archivos de content:// (Android)
    if (uri.startsWith('content://')) {
      // Generar nombre basado en timestamp
      const timestamp = Date.now();
      const fileName = `archivo_${timestamp}`;
      
      return {
        uri,
        name: fileName,
        type: 'application/octet-stream', // Tipo genérico
      };
    }
    
    // Fallback
    return {
      uri,
      name: 'archivo_desconocido',
      type: 'application/octet-stream',
    };
  } catch (error) {
    console.error('Error obteniendo metadatos del archivo:', error);
    return {
      uri,
      name: 'archivo_error',
      type: 'application/octet-stream',
    };
  }
};

/**
 * Determina el tipo MIME basado en la extensión del archivo
 */
export const getMimeTypeFromExtension = (extension: string): string => {
  const mimeTypes: { [key: string]: string } = {
    // Imágenes
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'webp': 'image/webp',
    'heic': 'image/heic',
    
    // Documentos
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'txt': 'text/plain',
    'csv': 'text/csv',
    
    // Audio
    'mp3': 'audio/mpeg',
    'wav': 'audio/wav',
    'ogg': 'audio/ogg',
    
    // Video
    'mp4': 'video/mp4',
    'avi': 'video/x-msvideo',
    'mov': 'video/quicktime',
    'wmv': 'video/x-ms-wmv',
    
    // Archivos comprimidos
    'zip': 'application/zip',
    'rar': 'application/x-rar-compressed',
    '7z': 'application/x-7z-compressed',
  };
  
  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
};

/**
 * Prepara un archivo para subida al servidor
 */
export const prepareFileForUpload = async (uri: string, fieldName: string): Promise<{
  uri: string;
  type: string;
  name: string;
}> => {
  const metadata = await getFileMetadata(uri);
  
  // Generar nombre único para evitar conflictos
  const timestamp = Date.now();
  const extension = metadata.name.split('.').pop() || 'bin';
  const uniqueName = `${fieldName}_${timestamp}.${extension}`;
  
  return {
    uri: metadata.uri,
    type: metadata.type,
    name: uniqueName,
  };
};

/**
 * Valida si un archivo es válido para subir
 */
export const validateFile = (uri: string, maxSizeBytes: number = 10 * 1024 * 1024): {
  isValid: boolean;
  error?: string;
} => {
  if (!uri) {
    return { isValid: false, error: 'URI de archivo vacía' };
  }
  
  if (!uri.startsWith('file://') && !uri.startsWith('content://')) {
    return { isValid: false, error: 'Tipo de URI no soportado' };
  }
  
  // TODO: Implementar validación de tamaño si es necesario
  // Esto requeriría acceso nativo para obtener el tamaño real del archivo
  
  return { isValid: true };
};

/**
 * Construye la URL para acceder a un archivo del servidor
 */
export const buildFileUrl = (filePath: string, userId: string): string => {
  if (!filePath) return '';
  
  // Si ya es una URL completa, devolverla tal como está
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath;
  }
  
  // Si es una ruta relativa, construir URL completa
  if (filePath.startsWith('/')) {
    const baseUrl = API_URL.replace('/ts/api', '');
    return `${baseUrl}${filePath}`;
  }
  
  // Para rutas que no empiezan con /, asumir que son rutas de archivo del servidor
  const baseUrl = API_URL.replace('/ts/api', '');
  return `${baseUrl}/uploads/${filePath}`;
};

/**
 * Obtiene la extensión de un archivo desde su nombre
 */
export const getFileExtension = (fileName: string): string => {
  return fileName.split('.').pop()?.toLowerCase() || '';
};

/**
 * Verifica si un archivo es una imagen
 */
export const isImageFile = (fileName: string): boolean => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'heic'];
  const extension = getFileExtension(fileName);
  return imageExtensions.includes(extension);
};

/**
 * Verifica si un archivo es un documento
 */
export const isDocumentFile = (fileName: string): boolean => {
  const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv'];
  const extension = getFileExtension(fileName);
  return documentExtensions.includes(extension);
};

/**
 * Obtiene un icono apropiado para el tipo de archivo
 */
export const getFileIcon = (fileName: string): string => {
  const extension = getFileExtension(fileName);

  if (isImageFile(fileName)) return 'image';
  if (extension === 'pdf') return 'document-text';
  if (['doc', 'docx'].includes(extension)) return 'document';
  if (['xls', 'xlsx'].includes(extension)) return 'grid';
  if (['ppt', 'pptx'].includes(extension)) return 'easel';
  if (extension === 'txt') return 'document-text-outline';
  if (['mp3', 'wav', 'ogg'].includes(extension)) return 'musical-notes';
  if (['mp4', 'avi', 'mov'].includes(extension)) return 'videocam';
  if (['zip', 'rar', '7z'].includes(extension)) return 'archive';

  return 'document-outline';
};

/**
 * Prepara FormData correctamente para envío al backend
 * Siguiendo la especificación del backend documentada
 * Ahora con procesamiento automático de content URIs
 */
export const prepareFormDataForSubmission = async (values: Record<string, any>): Promise<FormData> => {
  const timestamp = new Date().toISOString();
  const formData = new FormData();
  const normalValues: Record<string, any> = {};
  const fileFields: string[] = [];

  console.log(`📦 [${timestamp}] === INICIANDO prepareFormDataForSubmission ===`);
  console.log(`📦 [${timestamp}] Valores recibidos:`, values);

  // Procesar cada campo de forma asíncrona
  for (const [fieldId, value] of Object.entries(values)) {
    // Verificar si es un archivo (URI que empieza con file:// o content://)
    if (typeof value === 'string' && (value.startsWith('file://') || value.startsWith('content://'))) {
      fileFields.push(fieldId);

      console.log(`📎 [${timestamp}] Procesando archivo para campo ${fieldId}:`, value);

      try {
        // Obtener nombre del archivo
        const originalFileName = value.split('/').pop() || `file_${fieldId}`;
        const decodedFileName = decodeURIComponent(originalFileName);
        console.log(`📎 [${timestamp}] Nombre de archivo decodificado:`, {
          original: originalFileName,
          decoded: decodedFileName
        });

        // Procesar archivo (copiar si es content URI)
        console.log(`🔄 [${timestamp}] Procesando archivo con processFileForUpload...`);
        const processedFile = await processFileForUpload(value, decodedFileName);
        console.log(`✅ [${timestamp}] Archivo procesado:`, processedFile);

        // Si era content URI y la copia falló, mostrar advertencia
        if (processedFile.wasContentUri && !processedFile.copySuccessful) {
          console.warn(`⚠️ [${timestamp}] Content URI no pudo ser copiado para campo ${fieldId}, enviando URI original`);
        }

        // Agregar archivo al FormData
        const fileObject = {
          uri: processedFile.uri,
          type: processedFile.type,
          name: processedFile.name
        };
        console.log(`📎 [${timestamp}] Agregando archivo al FormData:`, {
          field: `file_${fieldId}`,
          fileObject
        });
        formData.append(`file_${fieldId}`, fileObject as any);

        console.log(`✅ [${timestamp}] Archivo procesado y agregado exitosamente:`, {
          field: `file_${fieldId}`,
          name: processedFile.name,
          type: processedFile.type,
          wasContentUri: processedFile.wasContentUri,
          copySuccessful: processedFile.copySuccessful,
          finalUri: processedFile.uri.substring(0, 50) + '...'
        });

      } catch (error) {
        console.error(`❌ [${timestamp}] Error procesando archivo en campo ${fieldId}:`, error);
        throw new Error(`Error procesando archivo en campo ${fieldId}: ${error}`);
      }

    } else if (Array.isArray(value)) {
      // Arrays (checkboxes) - convertir a JSON string
      normalValues[fieldId] = JSON.stringify(value);
      console.log(`☑️ [${timestamp}] Campo array ${fieldId}:`, {
        original: value,
        processed: normalValues[fieldId]
      });
    } else {
      // Valores normales
      normalValues[fieldId] = value || '';
      console.log(`📝 [${timestamp}] Campo normal ${fieldId}:`, {
        original: value,
        processed: normalValues[fieldId]
      });
    }
  }

  // Agregar valores normales como JSON string
  const normalValuesJson = JSON.stringify(normalValues);
  console.log(`📦 [${timestamp}] Agregando valores normales al FormData:`, {
    normalValues,
    jsonString: normalValuesJson
  });
  formData.append('values', normalValuesJson);

  console.log(`✅ [${timestamp}] FormData preparado exitosamente:`, {
    normalValues,
    fileFields,
    totalFields: Object.keys(values).length,
    fileFieldsCount: fileFields.length,
    normalFieldsCount: Object.keys(normalValues).length
  });

  console.log(`🏁 [${timestamp}] === FIN prepareFormDataForSubmission ===`);
  return formData;
};

/**
 * Copia un archivo content:// a un directorio accesible
 * Maneja graciosamente cuando react-native-fs no está disponible
 */
export const copyContentUriToAccessiblePath = async (contentUri: string, fileName: string): Promise<string> => {
  try {
    console.log('📋 Intentando copiar content URI:', contentUri);

    // Verificar si RNFS está disponible y tiene las propiedades necesarias
    if (!RNFS || typeof RNFS.DocumentDirectoryPath === 'undefined' || typeof RNFS.copyFile !== 'function') {
      console.warn('⚠️ react-native-fs no disponible o incompleto, usando URI original');
      throw new Error('react-native-fs no disponible');
    }

    // Verificar que DocumentDirectoryPath no sea null
    if (!RNFS.DocumentDirectoryPath) {
      console.warn('⚠️ DocumentDirectoryPath es null');
      throw new Error('DocumentDirectoryPath no disponible');
    }

    // Crear directorio de destino si no existe
    const destDir = `${RNFS.DocumentDirectoryPath}/uploads`;

    // Verificar si el directorio existe de forma segura
    let dirExists = false;
    try {
      dirExists = await RNFS.exists(destDir);
    } catch (existsError) {
      console.warn('⚠️ Error verificando directorio:', existsError);
      throw existsError;
    }

    if (!dirExists) {
      try {
        await RNFS.mkdir(destDir);
        console.log('📁 Directorio creado:', destDir);
      } catch (mkdirError) {
        console.warn('⚠️ Error creando directorio:', mkdirError);
        throw mkdirError;
      }
    }

    // Generar nombre único para evitar conflictos
    const timestamp = Date.now();
    const extension = fileName.split('.').pop() || 'bin';
    const uniqueFileName = `file_${timestamp}.${extension}`;
    const destPath = `${destDir}/${uniqueFileName}`;

    // Copiar archivo usando react-native-fs
    try {
      await RNFS.copyFile(contentUri, destPath);
      console.log('✅ Archivo copiado exitosamente:', {
        from: contentUri,
        to: destPath,
        fileName: uniqueFileName
      });
      return destPath;
    } catch (copyError) {
      console.warn('⚠️ Error copiando archivo:', copyError);
      throw copyError;
    }

  } catch (error) {
    console.error('❌ Error en copyContentUriToAccessiblePath:', error);
    // Fallback: devolver URI original si la copia falla
    console.log('🔄 Usando URI original como fallback');
    return contentUri;
  }
};

/**
 * Procesa un archivo, copiándolo si es necesario
 */
export const processFileForUpload = async (uri: string, fileName: string): Promise<{
  uri: string;
  name: string;
  type: string;
  wasContentUri: boolean;
  copySuccessful: boolean;
}> => {
  let finalUri = uri;
  let finalName = fileName;
  let wasContentUri = false;
  let copySuccessful = false;

  // Si es content URI, intentar copiarlo a directorio accesible
  if (uri.startsWith('content://')) {
    console.log('🔄 Procesando content URI...');
    wasContentUri = true;

    try {
      const copiedUri = await copyContentUriToAccessiblePath(uri, fileName);

      // Verificar si la copia fue exitosa (URI cambió)
      if (copiedUri !== uri && copiedUri.startsWith('file://')) {
        finalUri = copiedUri;
        finalName = copiedUri.split('/').pop() || fileName;
        copySuccessful = true;
        console.log('✅ Content URI copiado exitosamente');
      } else {
        console.warn('⚠️ Copia de content URI falló, usando URI original');
        finalUri = uri;
        copySuccessful = false;
      }
    } catch (error) {
      console.error('❌ Error procesando content URI:', error);
      finalUri = uri;
      copySuccessful = false;
    }
  }

  // Obtener tipo MIME
  const extension = getFileExtension(finalName);
  const mimeType = getMimeTypeFromExtension(extension);

  return {
    uri: finalUri,
    name: finalName,
    type: mimeType,
    wasContentUri,
    copySuccessful
  };
};

/**
 * Valida que no haya content:// URIs problemáticos
 */
export const validateFormValues = (values: Record<string, any>): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  Object.entries(values).forEach(([fieldId, value]) => {
    if (typeof value === 'string' && value.startsWith('content://')) {
      // Verificar si react-native-fs está disponible
      if (!RNFS || typeof RNFS.copyFile !== 'function') {
        errors.push(`Campo ${fieldId}: Content URI detectado pero react-native-fs no está disponible. Por favor, usa solo fotos de la cámara o galería.`);
      } else {
        warnings.push(`Campo ${fieldId}: Content URI detectado. Se intentará copiar automáticamente.`);
      }
    }

    if (typeof value === 'string' && value.startsWith('file://')) {
      // Verificar que el archivo sea accesible
      if (!value.includes('DocumentPicker') && !value.includes('ImagePicker') && !value.includes('uploads')) {
        warnings.push(`Campo ${fieldId}: URI de archivo puede no ser accesible.`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};
