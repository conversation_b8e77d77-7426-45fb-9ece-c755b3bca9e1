import { API_URL } from '../constants/config';
import axios from 'axios';

/**
 * Get list of available log files
 * @param dominio - Domain for the API endpoint
 * @returns Promise with the list of log files
 */
export const getLogFiles = async (dominio: string) => {
    try {
        const { data } = await axios.get(`${API_URL}/errors/files`, {
            params: { dominio }
        });
        return { 
            error: false, 
            data: data.files 
        };
    } catch (error: any) {
        console.error('❌ Error al obtener archivos de log:', error.response?.data || error.message);
        return {
            error: true,
            msg: error.response?.data?.message || 'No se pudieron obtener los archivos de log.',
            data: []
        };
    }
};

/**
 * Get content of a specific log file
 * @param dominio - Domain for the API endpoint
 * @param filename - Name of the log file
 * @param level - Filter by log level (error, warning, notice, info, debug)
 * @param search - Search text
 * @param limit - Maximum number of lines to return
 * @param offset - Number of lines to skip (for pagination)
 * @returns Promise with the log file content
 */
export const getLogFileContent = async (
    dominio: string,
    filename: string,
    level?: string,
    search?: string,
    limit: number = 100,
    offset: number = 0
) => {
    try {
        const { data } = await axios.get(`${API_URL}/errors/file/${filename}`, {
            params: {
                dominio,
                level,
                search,
                limit,
                offset
            }
        });
        return { 
            error: false, 
            data 
        };
    } catch (error: any) {
        console.error('❌ Error al obtener contenido del log:', error.response?.data || error.message);
        return {
            error: true,
            msg: error.response?.data?.message || 'No se pudo obtener el contenido del log.',
            data: {
                filename,
                total_lines: 0,
                lines: [],
                limit,
                offset
            }
        };
    }
};

/**
 * Get latest errors from all log files
 * @param dominio - Domain for the API endpoint
 * @param limit - Maximum number of errors to return
 * @param level - Log level to filter (default: error)
 * @returns Promise with the latest errors
 */
export const getLatestErrors = async (
    dominio: string,
    limit: number = 50,
    level: string = 'error'
) => {
    try {
        const { data } = await axios.get(`${API_URL}/errors/latest`, {
            params: {
                dominio,
                limit,
                level
            }
        });
        return { 
            error: false, 
            data: data.errors,
            count: data.count
        };
    } catch (error: any) {
        console.error('❌ Error al obtener últimos errores:', error.response?.data || error.message);
        return {
            error: true,
            msg: error.response?.data?.message || 'No se pudieron obtener los últimos errores.',
            data: [],
            count: 0
        };
    }
};