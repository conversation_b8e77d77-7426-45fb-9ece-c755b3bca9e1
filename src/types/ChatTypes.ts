export interface ApiSubscribe {
    conversation: object;
    messages: any[];
    topic: string;
    authorization: string;
}

export interface ApiPublishMessage {
    id: number;
    msj: string;
    code: number
}

export interface Message {
    id?: string;
    content: string;
    created_at?: string;
    author: {
        id: number;
        name: string;
        lastName: string;
        role: string[];
    }
}