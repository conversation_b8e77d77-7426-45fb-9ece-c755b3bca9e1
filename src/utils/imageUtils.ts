// src/utils/imageUtils.ts

/**
 * Valida si una URL de imagen es accesible
 */
export const validateImageUrl = async (url: string): Promise<boolean> => {
    try {
        console.log('🔍 Validando URL de imagen:', url);
        
        if (!url) {
            console.log('❌ URL vacía');
            return false;
        }

        // Verificar si es una URL válida
        try {
            new URL(url);
        } catch {
            console.log('❌ URL inválida:', url);
            return false;
        }

        // Hacer una petición HEAD para verificar si la imagen existe
        const response = await fetch(url, { method: 'HEAD' });
        console.log('📡 Response status:', response.status);
        console.log('📡 Content-Type:', response.headers.get('content-type'));
        
        if (response.ok) {
            const contentType = response.headers.get('content-type');
            const isImage = contentType?.startsWith('image/');
            console.log('✅ URL accesible, es imagen:', isImage);
            return isImage || false;
        } else {
            console.log('❌ URL no accesible, status:', response.status);
            return false;
        }
    } catch (error) {
        console.error('❌ Error al validar URL:', error);
        return false;
    }
};

/**
 * Analiza una lista de posts y valida sus imágenes
 */
export const analyzePostImages = async (posts: any[]) => {
    console.log('🔍 Analizando imágenes de posts...');
    
    for (let i = 0; i < posts.length; i++) {
        const post = posts[i];
        console.log(`\n📝 Analizando post ${i + 1}/${posts.length}:`);
        console.log('  - ID:', post.id);
        console.log('  - Título:', post.title);
        console.log('  - URL imagen:', post.image);
        
        if (post.image) {
            const isValid = await validateImageUrl(post.image);
            console.log('  - ✅ Imagen válida:', isValid);
        } else {
            console.log('  - ❌ Sin imagen');
        }
    }
};

/**
 * Convierte URLs relativas a absolutas y corrige URLs malformadas
 */
export const normalizeImageUrl = (url: string, baseUrl?: string): string => {
    if (!url) return url;

    //console.log('🔧 Normalizando URL:', url);

    // CORRECCIÓN CRÍTICA: Reemplazar localhost/127.0.0.1 con la IP correcta
    if (url.includes('localhost') || url.includes('127.0.0.1')) {
        console.warn('🚨 URL con localhost detectada, corrigiendo:', url);
        const correctedUrl = url
            .replace(/localhost:8004/g, '***************:8004')
            .replace(/127\.0\.0\.1:8004/g, '***************:8004');
        console.log('✅ URL corregida:', correctedUrl);
        return correctedUrl;
    }

    // VERIFICACIÓN ADICIONAL: Log de todas las URLs para debugging
/*    console.log('🔍 URL procesada por normalizeImageUrl:', {
        original: url,
        esUrlCompleta: url.startsWith('http'),
        contieneIP: url.includes('***************'),
        contienePuerto: url.includes(':8004')
    });*/

    // Corregir dobles slashes en URLs absolutas (problema común del backend)
    if (url.startsWith('http://') || url.startsWith('https://')) {
        // Separar protocolo del resto
        const [protocol, ...rest] = url.split('://');
        const restUrl = rest.join('://');

        // Reemplazar múltiples slashes consecutivos con uno solo
        const cleanedUrl = restUrl.replace(/\/+/g, '/');
        const normalizedUrl = `${protocol}://${cleanedUrl}`;

        //console.log('✅ URL normalizada:', normalizedUrl);
        return normalizedUrl;
    }

    // Si es una URL relativa y tenemos baseUrl, combinarlas
    if (baseUrl) {
        const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
        const cleanUrl = url.startsWith('/') ? url : `/${url}`;
        return `${cleanBaseUrl}${cleanUrl}`;
    }

    return url;
};

/**
 * Obtiene información detallada de una imagen
 */
export const getImageInfo = async (url: string) => {
    try {
        const response = await fetch(url, { method: 'HEAD' });
        return {
            url,
            accessible: response.ok,
            status: response.status,
            contentType: response.headers.get('content-type'),
            contentLength: response.headers.get('content-length'),
            lastModified: response.headers.get('last-modified'),
        };
    } catch (error) {
        return {
            url,
            accessible: false,
            error: error.message,
        };
    }
};
