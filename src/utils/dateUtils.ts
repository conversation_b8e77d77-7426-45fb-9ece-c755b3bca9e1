// utils/dateUtils.ts

export function formatFecha(fecha: string): string {
    if (!fecha) return 'Sin fecha';

    try {
        const fechaSolo = fecha.split(' ')[0];
        const [anio, mes, dia] = fechaSolo.split('-');

        const meses = [
            'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
            'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
        ];

        const mesNombre = meses[parseInt(mes, 10) - 1];

        return `${parseInt(dia, 10)} ${mesNombre} ${anio}`;
    } catch (e) {
        return 'Fecha inválida';
    }
}

export function parseDateTimeToUTC(dateTimeString: string): Date {
    // Separar fecha y hora
    const [datePart, timePart] = dateTimeString.split(' ');
    const [year, month, day] = datePart.split('-').map(Number);
    const [hour, minute, second] = timePart.split(':').map(Number);

    // Crear la fecha en UTC (nota: mes - 1 porque en JS enero = 0)
    return new Date(Date.UTC(year, month - 1, day, hour, minute, second));
}

