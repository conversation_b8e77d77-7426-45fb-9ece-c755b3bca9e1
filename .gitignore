# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# ===================================
# DEPENDENCIES
# ===================================
node_modules/
npm-debug.*
yarn-debug.*
yarn-error.*
package-lock.json
yarn.lock

# ===================================
# EXPO & REACT NATIVE
# ===================================
.expo/
dist/
web-build/
expo-env.d.ts

# Native builds
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.keystore

# React Native
.android/
.ios/
android/app/build/
ios/build/
ios/Pods/
ios/*.xcworkspace

# Metro bundler
.metro-health-check*
metro.config.js.bak

# ===================================
# ENVIRONMENT & CONFIG
# ===================================
# Environment files
.env
.env.local
.env.development
.env.test
.env.production
.env*.local

# Config files with sensitive data
google-services.json
GoogleService-Info.plist
appts-9fc8d-firebase-adminsdk-fbsvc-795d257168.json

# API Configuration (uncomment if needed)
# src/constants/config.ts

# ===================================
# DEVELOPMENT & DEBUGGING
# ===================================
# TypeScript
*.tsbuildinfo

# Testing
coverage/
.nyc_output/
junit.xml

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ===================================
# OPERATING SYSTEM
# ===================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
*.pem

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# ===================================
# IDE & EDITORS
# ===================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA
.idea/
*.iml
*.iws

# Android Studio
*.iml
.gradle/
local.properties

# Xcode
*.xcodeproj/*
!*.xcodeproj/project.pbxproj
!*.xcodeproj/xcshareddata/
!*.xcodeproj/project.xcworkspace/
*.xcworkspace/*
!*.xcworkspace/contents.xcworkspacedata
.xcode.env.local

# ===================================
# ASSETS & UPLOADS
# ===================================
# User uploaded files
uploads/
temp/
tmp/

# Generated assets
assets/generated/

# ===================================
# BUILD & DISTRIBUTION
# ===================================
# EAS Build
.eas/

# APK files
*.apk
*.aab

# iOS builds
*.ipa

# ===================================
# SECURITY & SENSITIVE DATA
# ===================================
# Private keys
*.pem
*.key
*.p12
*.p8
*.mobileprovision

# Certificates
*.cer
*.crt

# Secrets
secrets.json
.secrets

# ===================================
# TEMPORARY & CACHE
# ===================================
# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# Temporary files
*.tmp
*.temp
.tmp/

# ===================================
# DOCUMENTATION & NOTES
# ===================================
# Personal notes (uncomment if needed)
# TODO.md
# NOTES.md
# .notes/

# ===================================
# CUSTOM PROJECT FILES
# ===================================
# Add your custom ignores here

# ===================================
# DEVELOPMENT REPORTS & DOCUMENTATION
# ===================================
# Bug analysis and correction reports
BUG_ANALYSIS_REPORT.md
SMS_DEBUGGING_REPORT.md
DOUBLE_SMS_FIX_REPORT.md
PRODUCTION_CHECKLIST.md
GITIGNORE_RECOMMENDATIONS.md

# Development notes and temporary docs
*_REPORT.md
*_CHECKLIST.md
*_DEBUGGING*.md
*_FIX*.md
*_ANALYSIS*.md

# Personal development notes
NOTES.md
TODO.md
FIXES.md
BUGS.md
CHANGELOG_DEV.md

# Temporary development files
*.dev.md
*.temp.md
*.draft.md
dev-*.md
temp-*.md

# Development logs and debugging
debug-*.log
dev-*.log
*.debug.txt